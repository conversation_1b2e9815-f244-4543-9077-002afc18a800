name: "Compound commands: subshell"
cases:
  - name: "Basic subshell usage"
    stdin: |
      (echo hi)

  - name: "Subshells in sequence"
    ignore_stderr: true
    stdin: |
      (echo hi)(echo there)

  - name: "Subshell env usage"
    stdin: |
      (subshell_var=value && echo "subshell_var: ${subshell_var}")
      echo "subshell_var: ${subshell_var}"

  - name: "Subshell output redirection"
    stdin: |
      (echo Hello; echo world) >out.txt
      echo "Dumping out.txt..."
      cat out.txt

  - name: "Piped subshell usage"
    stdin: |
      (echo hi) | wc -l

  - name: "Breaks in subshell"
    stdin: |
      for i in 1 2 3; do
        echo $i
        (for i in 1 2 3; do break 2; done)
      done
