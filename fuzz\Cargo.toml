[package]
name = "brush-fuzz"
description = "Fuzz tests for brush"
publish = false
version = "0.2.2"
authors.workspace = true
categories.workspace = true
edition.workspace = true
keywords.workspace = true
license.workspace = true
readme.workspace = true
repository.workspace = true
rust-version.workspace = true

[package.metadata]
cargo-fuzz = true

[lints]
workspace = true

[dependencies]
anyhow = "1.0.98"
assert_cmd = "2.0.17"
libfuzzer-sys = "0.4"
tokio = { version = "1.47.1", features = ["rt"] }

[dependencies.brush-core]
path = "../brush-core"

[dependencies.brush-parser]
path = "../brush-parser"
features = ["fuzz-testing"]

[[bin]]
name = "fuzz_parse"
path = "fuzz_targets/fuzz_parse.rs"
test = false
doc = false
bench = false

[[bin]]
name = "fuzz_arithmetic"
path = "fuzz_targets/fuzz_arithmetic.rs"
test = false
doc = false
bench = false
