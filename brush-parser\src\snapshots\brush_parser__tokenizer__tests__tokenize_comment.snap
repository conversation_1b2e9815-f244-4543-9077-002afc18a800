---
source: brush-parser/src/tokenizer.rs
expression: "test_tokenizer(r\"a #comment\n\")?"
---
TokenizerResult(
  input: "a #comment\n",
  result: [
    W("a", Loc(
      start: Pos(
        idx: 0,
        line: 1,
        col: 1,
      ),
      end: Pos(
        idx: 1,
        line: 1,
        col: 2,
      ),
    )),
    Op("\n", Loc(
      start: Pos(
        idx: 2,
        line: 1,
        col: 3,
      ),
      end: Pos(
        idx: 11,
        line: 2,
        col: 1,
      ),
    )),
  ],
)
