name: "Process"
common_test_files:
  - path: "process-helpers.sh"
    source_path: "../utils/process-helpers.sh"

cases:
  - name: "Basic process"
    stdin: |
      # TODO: Figure out how to make this work elsewhere
      if [[ "$(uname)" != "Linux" ]]; then
        echo "Skipping test on non-Linux platform"
        exit 0
      fi

      source process-helpers.sh
      echo "pid != ppid: $(( $(get-pid) != $(get-ppid) ))"
      echo "pid == pgrp: $(( $(get-pid) != $(get-pgrp) ))"
