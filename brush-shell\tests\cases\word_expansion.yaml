name: "Word expansion"
cases:
  - name: "Undefined variables"
    stdin: |
      echo "Undefined: ${undefined}"
      echo "Undefined: $undefined"

  - name: "Defined variables"
    stdin: |
      var=xyz
      echo "Defined: ${var}"
      echo "Defined: var"
      echo "Defined: prefix${var}suffix"
      echo "Defined: prefix$var"

  - name: "Undefined positional params"
    stdin: |
      echo "Param: $9"
      echo "Param: ${9}"

  - name: "High-numbered positional params"
    stdin: |
      echo "Param: $99"
      echo "Param: ${99}"

  - name: "Single quotes"
    stdin: |
      var=xyz
      echo 'Quoted string'
      echo 'abc ${var} def'

  - name: "Double quotes"
    stdin: |
      echo "\""

  - name: "ANSI-C quotes"
    stdin: |
      single_quoted='\n'
      echo "Single quoted len: ${#single_quoted}"
      echo -n '\n' | hexdump -C

      ansi_c_quoted=$'\n'
      echo "ANSI-C quoted len: ${#ansi_c_quoted}"
      echo -n $'\n' | hexdump -C

  - name: "ANSI-C quotes with escaped single quote"
    stdin: |
      ansi_c_quoted=$'\''
      echo "ANSI-C quoted single quote len: ${#ansi_c_quoted}"
      echo -n $'\'' | hexdump -C

  - name: "gettext style quotes"
    stdin: |
      quoted=$"Hello, world"
      echo "Content: [${quoted}]"

  - name: "Command substitution"
    stdin: |
      var="value"

      echo "1:"
      echo $(echo hi)
      echo "2:"
      echo "$(echo hi)"
      echo "3:"
      echo "$(echo "hi")"
      echo "4:"
      echo "$(var="updated"; echo ${var})"
      echo "var=${var}"

  - name: "Command substitution with embedded parens"
    stdin: |
      x=$(echo foo | (wc -l; echo hi))
      echo "\$x: $x"

  - name: "Command substitution with subshell"
    stdin: |
      x=$( (echo hi) )
      echo "\$x: $x"

  - name: "Command substitution exit code"
    stdin: |
      x=$(false) && echo "1. Made it past false"
      y=$(true) && echo "2. Made it past true"

  - name: "Command substitution with exec"
    known_failure: true
    stdin: |
      x=$(exec echo hi)
      echo "x: $x"

  - name: "Backtick command substitution"
    stdin: |
      echo `echo hi`

  - name: "Backtick command substitution with escaping"
    stdin: |
      echo `echo \`echo hi\``

  - name: "Backtick command substitution with backslashes"
    stdin: |
      echo `echo \\`

  - name: "Backtick command substitution in double quotes"
    stdin: |
      echo "`echo First line`"
      echo " `echo Second line` "
      echo "Third`echo line`here"

  - name: "String length"
    stdin: |
      x="abc"
      echo "len = ${#x}"

  - name: "Array access"
    stdin: |
      y=(a b c)

      echo ${y[@]}
      echo ${y[*]}

      echo "\${y[@]}: ${y[@]}"
      echo "\${y[*]}: ${y[*]}"

      var1="${y[@]}"
      echo "var1: ${var1}"
      echo $var1

      var2=${y[@]}
      echo "var2: ${var2}"
      echo $var2

      var3="${y[*]}"
      echo "var3: ${var3}"
      echo $var3

      var4=${y[*]}
      echo "var4: ${var4}"
      echo $var4

      for f in "${y[@]}"; do
          echo "quoted-@ => Element: $f"
      done

      for f in ${y[@]}; do
          echo "@ => Element: $f"
      done

      for f in "${y[*]}"; do
          echo "quoted-* => Element: $f"
      done

      for f in ${y[*]}; do
          echo "* => Element: $f"
      done

  - name: "Empty arrays"
    stdin: |
      myarray=()

      for f in "${myarray[@]}"; do
        echo "Quoted at-sign element: |$f|"
      done

      for f in ${myarray[@]}; do
        echo "Unquoted at-sign element: |$f|"
      done

      for f in "${myarray[*]}"; do
        echo "Quoted at-sign element: |$f|"
      done

      for f in ${myarray[*]}; do
        echo "Unquoted at-sign element: |$f|"
      done

  - name: "Mixing array elements and other words"
    stdin: |
      myarray=(a b c)

      for f in "${myarray[@]} d e f"; do
        echo "ELEMENT: $f"
      done

      for f in ${myarray[@]} d e f; do
        echo "ELEMENT: $f"
      done

  - name: "Passing array elements to func"
    stdin: |
      myfunc() {
        echo "In function"
        for arg in $@; do
          echo "ARG: |${arg}|"
        done
      }

      arr=(a b c "" "e f")
      myfunc ${arr[@]}
      myfunc "${arr[@]}"

  - name: "Array length"
    stdin: |
      y=(a b c)
      echo "len(y)    = ${#y}"
      echo "len(y[*]) = ${#y[*]}"
      echo "len(y[@]) = ${#y[@]}"

  - name: "Array length: single item"
    stdin: |
      z=("something here")
      echo "len(z)    = ${#z}"
      echo "len(z[*]) = ${#z[*]}"
      echo "len(z[@]) = ${#z[@]}"

  - name: "Parameter expression: default value"
    stdin: |
      value="value"
      empty=""
      declare declared
      unset undeclared

      # -
      echo "\${value-default}       : ${value-default}"
      echo "\${empty-default}       : ${empty-default}"
      echo "\${declared-default}    : ${declared-default}"
      echo "\${undeclared-default}  : ${undeclared-default}"

      echo "\${value-}              : ${value-}"
      echo "\${empty-}              : ${empty-}"
      echo "\${declared-}           : ${declared-}"
      echo "\${undeclared-}         : ${undeclared-}"

      # :-
      echo "\${value:-default}      : ${value:-default}"
      echo "\${empty:-default}      : ${empty:-default}"
      echo "\${declared:-default}   : ${declared:-default}"
      echo "\${undeclared:-default} : ${undeclared:-default}"

      echo "\${value:-}             : ${value:-}"
      echo "\${empty:-}             : ${empty:-}"
      echo "\${declared:-}          : ${declared:-}"
      echo "\${undeclared:-}        : ${undeclared:-}"

  - name: "Parameter expression: assign default value (no colon)"
    stdin: |
      value="value"
      empty=""
      declare declared
      unset undeclared

      # =
      echo "\${value=default}       : ${value=default}"
      declare -p value
      echo "\${empty=default}       : ${empty=default}"
      declare -p empty
      echo "\${declared=default}    : ${declared=default}"
      declare -p declared
      echo "\${undeclared=default}  : ${undeclared=default}"
      declare -p undeclared

  - name: "Parameter expression: assign default value (no colon, empty replacement)"
    stdin: |
      value="value"
      empty=""
      declare declared
      unset undeclared

      # =
      echo "\${value=}              : ${value=}"
      declare -p value
      echo "\${empty=}              : ${empty=}"
      declare -p empty
      echo "\${declared=}           : ${declared=}"
      declare -p declared
      echo "\${undeclared=}         : ${undeclared=}"
      declare -p undeclared

  - name: "Parameter expression: assign default value (colon)"
    stdin: |
      value="value"
      empty=""
      declare declared
      unset undeclared

      # :=
      echo "\${value:=default}       : ${value:=default}"
      declare -p value
      echo "\${empty:=default}       : ${empty:=default}"
      declare -p empty
      echo "\${declared:=default}    : ${declared:=default}"
      declare -p declared
      echo "\${undeclared:=default}  : ${undeclared:=default}"
      declare -p undeclared

  - name: "Parameter expression: assign default value (colon, empty replacement)"
    stdin: |
      value="value"
      empty=""
      declare declared
      unset undeclared

      # :=
      echo "\${value:=}              : ${value:=}"
      declare -p value
      echo "\${empty:=}              : ${empty:=}"
      declare -p empty
      echo "\${declared:=}           : ${declared:=}"
      declare -p declared
      echo "\${undeclared:=}         : ${undeclared:=}"
      declare -p undeclared

  - name: "Parameter expression: alternative value"
    stdin: |
      value="value"
      empty=""
      declare declared
      unset undeclared

      # +
      echo "\${value+default}       : ${value+default}"
      echo "\${empty+default}       : ${empty+default}"
      echo "\${declared+default}    : ${declared+default}"
      echo "\${undeclared+default}  : ${undeclared+default}"

      echo "\${value+}              : ${value+}"
      echo "\${empty+}              : ${empty+}"
      echo "\${declared+}           : ${declared+}"
      echo "\${undeclared+}         : ${undeclared+}"

      # :+
      echo "\${value:+default}      : ${value:+default}"
      echo "\${empty:+default}      : ${empty:+default}"
      echo "\${declared:+default}   : ${declared:+default}"
      echo "\${undeclared:+default} : ${undeclared:+default}"

      echo "\${value:+}             : ${value:+}"
      echo "\${empty:+}             : ${empty:+}"
      echo "\${declared:+}          : ${declared:+}"
      echo "\${undeclared:+}        : ${undeclared:+}"

  - name: "Parameter expression: error on condition (interactive)"
    skip: true # TODO: fails with cargo nextest
    ignore_stderr: true
    args: ["-i"]
    stdin: |
      value="value"
      empty=""
      declare declared
      unset undeclared

      # ?
      echo "\${value?error message}       : ${value?error message}"
      echo "  -> result: $?"
      echo "\${empty?error message}       : ${empty?error message}"
      echo "  -> result: $?"
      echo "\${declared?error message}    : ${declared?error message}"
      echo "  -> result: $?"
      echo "\${undeclared?error message}  : ${undeclared?error message}"
      echo "  -> result: $?"

      echo "\${value?}                    : ${value?}"
      echo "  -> result: $?"
      echo "\${empty?}                    : ${empty?}"
      echo "  -> result: $?"
      echo "\${declared?}                 : ${declared?}"
      echo "  -> result: $?"
      echo "\${undeclared?}               : ${undeclared?}"
      echo "  -> result: $?"

      # :?
      echo "\${value:?error message}      : ${value:?error message}"
      echo "  -> result: $?"
      echo "\${empty:?error message}      : ${empty:?error message}"
      echo "  -> result: $?"
      echo "\${declared:?error message}   : ${declared:?error message}"
      echo "  -> result: $?"
      echo "\${undeclared:?error message} : ${undeclared:?error message}"
      echo "  -> result: $?"

      echo "\${value:?}                   : ${value:?}"
      echo "  -> result: $?"
      echo "\${empty:?}                   : ${empty:?}"
      echo "  -> result: $?"
      echo "\${declared:?}                : ${declared:?}"
      echo "  -> result: $?"
      echo "\${undeclared:?}              : ${undeclared:?}"
      echo "  -> result: $?"

  - name: "Parameter expression: error on condition (non-interactive)"
    known_failure: true # TODO: needs triage and debugging
    ignore_stderr: true
    stdin: |
      echo "${non_existent_var?error message}"
      echo "This should never execute"

  - name: "Parameter expression: expanded array as alternate value"
    stdin: |
      declare -a var=("abc" "def" "ghi" "")

      for item in "${var[@]}"; do
        echo "Item: '${item}'"
      done

      echo "Expression 1: '${var+"${var[@]}"}'"
      for item in "${var+"${var[@]}"}"; do
        echo "  -> '${item}'"
      done

      echo "Expression 2: '${var+${var[@]}}'"
      for item in "${var+${var[@]}}"; do
        echo "  -> '${item}'"
      done

      echo "Expression 3: '${var+"${var[@]}"}'"
      for item in ${var+"${var[@]}"}; do
        echo "  -> '${item}'"
      done

      echo "Expression 4: '${var+${var[@]}}'"
      for item in ${var+${var[@]}}; do
        echo "  -> '${item}'"
      done

      echo "Expression 5: '${var+"${var[*]}"}'"
      for item in "${var+"${var[*]}"}"; do
        echo "  -> '${item}'"
      done

      echo "Expression 6: '${var+${var[*]}}'"
      for item in "${var+${var[*]}}"; do
        echo "  -> '${item}'"
      done

      echo "Expression 7: '${var+"${var[*]}"}'"
      for item in ${var+"${var[*]}"}; do
        echo "  -> '${item}'"
      done

      echo "Expression 8: '${var+${var[*]}}'"
      for item in ${var+${var[*]}}; do
        echo "  -> '${item}'"
      done

  - name: "Parameter expression: expanded array as default value"
    known_failure: true # TODO: needs triage and debugging
    stdin: |
      declare -a var=("abc" "def" "ghi" "")

      for item in "${var[@]}"; do
        echo "Item: '${item}'"
      done

      echo "Expression 1: ${nonexistent-${var[@]}}"
      for item in ${nonexistent-${var[@]}}; do
        echo "  -> '${item}'"
      done

      echo "Expression 2: ${nonexistent-"${var[@]}"}"
      for item in ${nonexistent-"${var[@]}"}; do
        echo "  -> '${item}'"
      done

      echo "Expression 3: ${nonexistent2=${var[@]}}"
      for item in ${nonexistent3=${var[@]}}; do
        echo "  -> '${item}'"
      done

      echo "Expression 4: ${nonexistent4="${var[@]}"}"
      for item in ${nonexistent5-"${var[@]}"}; do
        echo "  -> '${item}'"
      done

  - name: "Remove prefix/suffix"
    stdin: |
      var="prepre-abc-sufsuf"

      # Smallest suffix
      shopt -u nocasematch
      echo "\${var%}:    ${var%}"
      echo "\${var%pre}: ${var%pre}"
      echo "\${var%suf}: ${var%suf}"
      echo "\${var%SUF}: ${var%SUF}"
      shopt -s nocasematch
      echo "\${var%SUF}(nocasematch): ${var%SUF}"

      # Largest suffix
      shopt -u nocasematch
      echo "\${var%%}:    ${var%%}"
      echo "\${var%%pre}: ${var%%pre}"
      echo "\${var%%suf}: ${var%%suf}"
      echo "\${var%%SUF}: ${var%%SUF}"
      shopt -s nocasematch
      echo "\${var%%SUF}(nocasematch): ${var%%SUF}"

      # Smallest prefix
      shopt -u nocasematch
      echo "\${var#}:     ${var#}"
      echo "\${var#pre}:  ${var#pre}"
      echo "\${var#suf}:  ${var#suf}"
      echo "\${var#PRE}:  ${var#PRE}"
      shopt -s nocasematch
      echo "\${var#PRE}(nocasematch):  ${var#PRE}"

      # Largest prefix
      shopt -u nocasematch
      echo "\${var##}:    ${var##}"
      echo "\${var##pre}: ${var##pre}"
      echo "\${var##suf}: ${var##suf}"
      echo "\${var##PRE}: ${var##PRE}"
      shopt -s nocasematch
      echo "\${var##PRE}(nocasematch): ${var##PRE}"

  - name: "Indirect variable references"
    stdin: |
      var="Hello"
      ref="var"
      echo "${!ref}"
      echo "${!ref//l/o}"

  - name: "Indirect variable references with special parameters"
    stdin: |
      set a b c

      ref="2"
      echo "${!ref}"

  - name: "Indirect variable references with array"
    stdin: |
      arr=("element1" "element2" "element3")

      ref="arr[1]"
      echo "${!ref}"

      ref="arr[10]"
      echo "${!ref}"

  - name: "Variable prefix match"
    stdin: |
      var1="Hello"
      var2="World"

      echo "${!var*}"
      echo "${!var@}"

      echo "Dumping *"
      for i in "${!var*}"; do
          echo "i: $i"
      done

      echo "Dumping @"
      for i in "${!var@}"; do
          echo "i: $i"
      done

  - name: "Array keys: indexed array"
    stdin: |
      arr=("element1" "element2" "element3")
      echo "@: ${!arr[@]}"
      echo "*: ${!arr[*]}"

      echo "Dumping [@]"
      for i in "${!arr[@]}"; do
          echo "@i: $i"
      done

      echo "Dumping [*]"
      for i in "${!arr[*]}"; do
          echo "*i: $i"
      done

  - name: "Array keys: empty array"
    stdin: |
      arr=()
      echo "@: ${!arr[@]}"
      echo "*: ${!arr[*]}"

      echo "Dumping [@]"
      for i in "${!arr[@]}"; do
          echo "@i: $i"
      done

      echo "Dumping [*]"
      for i in "${!arr[*]}"; do
          echo "*i: $i"
      done

  - name: "Uppercase first character"
    stdin: |
      var="hello"

      shopt -u nocasematch
      echo "\${var^}:   ${var^}"
      echo "\${var^h}:  ${var^h}"
      echo "\${var^H}:  ${var^H}"
      echo "\${var^l}:  ${var^l}"
      echo "\${var^h*}: ${var^h*}"
      echo "\${var^he}: ${var^he}"
      echo "\${var^?}:  ${var^?}"
      echo "\${var^*}:  ${var^*}"

      shopt -s nocasematch
      echo "\${var^H}(nocasematch): ${var^H}"

      arr=("hello" "world")
      echo "\${arr^}: ${arr^}"
      echo "\${arr[@]^}: ${arr[@]^}"
      echo "\${arr[*]^}: ${arr[*]^}"

  - name: "Uppercase matching pattern"
    stdin: |
      var="hello"

      shopt -u nocasematch
      echo "\${var^^}:  ${var^^}"
      echo "\${var^^l}: ${var^^l}"
      echo "\${var^^L}: ${var^^L}"
      echo "\${var^^m}: ${var^^m}"

      shopt -s nocasematch
      echo "\${var^^L}(nocasematch): ${var^^L}"

      arr=("hello" "world")
      echo "\${arr^^}: ${arr^^}"
      echo "\${arr[@]^^}: ${arr[@]^^}"
      echo "\${arr[*]^^}: ${arr[*]^^}"

  - name: "Lowercase first character"
    stdin: |
      var="HELLO"

      shopt -u nocasematch
      echo "\${var,}:   ${var,}"
      echo "\${var,h}:  ${var,h}"
      echo "\${var,H}:  ${var,H}"
      echo "\${var,L}:  ${var,L}"
      echo "\${var,H*}: ${var,H*}"
      echo "\${var,HE}: ${var,HE}"
      echo "\${var,?}:  ${var,?}"
      echo "\${var,*}:  ${var,*}"

      shopt -s nocasematch
      echo "\${var,h}(nocasematch):  ${var,h}"

      arr=("HELLO" "WORLD")
      echo "\${arr,}: ${arr,}"
      echo "\${arr[@],}: ${arr[@],}"
      echo "\${arr[*],}: ${arr[*],}"

  - name: "Lowercase matching pattern"
    stdin: |
      var="HELLO"

      shopt -u nocasematch
      echo "\${var,,}:  ${var,,}"
      echo "\${var,,M}: ${var,,M}"
      echo "\${var,,L}: ${var,,L}"
      echo "\${var,,l}: ${var,,l}"

      shopt -s nocasematch
      echo "\${var,,l}(nocasematch): ${var,,l}"

      arr=("HELLO" "WORLD")
      echo "\${arr,,}: ${arr,,}"
      echo "\${arr[@],,}: ${arr[@],,}"
      echo "\${arr[*],,}: ${arr[*],,}"

  - name: "Substring replacement"
    stdin: |
      var="Hello, world!"
      echo "\${var/world/WORLD}: ${var/world/WORLD}"

      arr=("world" "world")
      echo "\${arr/world/WORLD}: ${arr/world/WORLD}"
      echo "\${arr[@]/world/WORLD}: ${arr[@]/world/WORLD}"
      echo "\${arr[*]/world/WORLD}: ${arr[*]/world/WORLD}"

  - name: "Substring replacement with slashes"
    stdin: |
      var="Hello, world!"
      echo "\${var/world//world}: ${var/world//world}"

  - name: "Substring replacement with patterns"
    stdin: |
      var="a[bc]d"
      pattern="[bc]"
      echo "Replacement 1: ${var/${pattern}/}"
      echo "Replacement 2: ${var/"${pattern}"/}"

  - name: "Prefix substring replacement"
    stdin: |
      var="Hello, world!"
      echo "\${var/#world/WORLD}: ${var/#world/WORLD}"
      echo "\${var/#Hello/HELLO}: ${var/#Hello/HELLO}"

      arr=("world" "world")
      echo "\${arr/#world/WORLD}: ${arr/#world/WORLD}"
      echo "\${arr[@]/#world/WORLD}: ${arr[@]/#world/WORLD}"
      echo "\${arr[*]/#world/WORLD}: ${arr[*]/#world/WORLD}"

  - name: "Suffix substring replacement"
    stdin: |
      var="Hello, world!"
      echo "\${var/%Hello/HELLO}:   ${var/%Hello/HELLO}"
      echo "\${var/%world!/WORLD!}: ${var/%world!/WORLD!}"

      arr=("world" "world")
      echo "\${arr/%world/WORLD}: ${arr/%world/WORLD}"
      echo "\${arr[@]/%world/WORLD}: ${arr[@]/%world/WORLD}"
      echo "\${arr[*]/%world/WORLD}: ${arr[*]/%world/WORLD}"

  - name: "Global substring replacement"
    stdin: |
      var="Hello, world, world!"
      echo "\${var//world/WORLD}: ${var//world/WORLD}"

      arr=("world world" "world world")
      echo "\${arr//world/WORLD}: ${arr//world/WORLD}"
      echo "\${arr[@]//world/WORLD}: ${arr[@]//world/WORLD}"
      echo "\${arr[*]//world/WORLD}: ${arr[*]//world/WORLD}"

  - name: "Global substring removal"
    stdin: |
      var="That is not all"

      shopt -u nocasematch
      echo "\${var//not }: ${var//not}"

      shopt -s nocasematch
      echo "\${var//NOT }: ${var//NOT }"

  - name: "Substring from offset"
    stdin: |
      var="Hello, world!"
      echo "\${var:0}:  ${var:0}"
      echo "\${var:7}:  ${var:7}"
      echo "\${var:50}: ${var:50}"
      echo "\${var:-1}: ${var:-1}"

  - name: "Substring with length"
    stdin: |
      var="Hello, world!"
      echo "\${var:0:1}:  ${var:0:1}"
      echo "\${var:0:0}:  ${var:0:0}"
      echo "\${var:0:50}: ${var:0:50}"
      echo "\${var:0:-1}: ${var:0:-1}"
      echo "\${var:0:-3}: ${var:0:-3}"
      echo "\${var:7:3}:  ${var:7:3}"
      echo "\${var:50:2}: ${var:50:2}"
      echo "\${var:-1:1}: ${var:-1:1}"
      echo "\${var:-3:1}: ${var:-3:1}"

  - name: "Substring on string with multi-byte chars"
    skip: true # TODO: succeeds on macOS, fails on Linux
    stdin: |
      var="7❌🖌️✅😂🔴⛔ABC"
      echo "${var:2}" | hexdump -C

  - name: "Substring operator on arrays"
    stdin: |
      set abcde fghij klmno pqrst uvwxy z
      echo "\${@:2:2}: ${@:2:2}"
      echo "\${@:2}: ${@:2}"

      myarray=(abcde fghij klmno pqrst uvwxy z)
      echo "\${myarray[@]:2:2}: ${myarray[@]:2:2}"
      echo "\${myarray[@]:2}: ${myarray[@]:2}"

  - name: "Substring operator on arrays with negative index"
    stdin: |
      set abcde fghij klmno pqrst uvwxy z
      echo "\${@: -2:2}: ${@: -2:2}"
      echo "\${@: -2}: ${@: -2}"

      myarray=(abcde fghij klmno pqrst uvwxy z)
      echo "\${myarray[@]: -2:2}: ${myarray[@]: -2:2}"
      echo "\${myarray[@]: -2}: ${myarray[@]: -2}"

  - name: "Substring operator on single-element array"
    stdin: |
      myarray=("abcde fghij klmno pqrst uvwxy z")
      echo "\${myarray[@]:0:1}: ${myarray[@]:0:1}"
      echo "\${myarray[@]:1}: ${myarray[@]:1}"

  - name: "Substring operator past end of array"
    stdin: |
      set a b c
      declare -a result1=("${@:3}")
      declare -p result1

      myarray=(a b c)
      declare -a result2=("${myarray[@]:3}")
      declare -p result2

  - name: "Substring with length (with nested expressions)"
    stdin: |
      var="Hello, world!"
      offset=7
      length=5
      echo "\${var:\$offset:\${length}}: ${var:$offset:${length}}"

  - name: "Parameter case transformation"
    stdin: |
      var="hElLo WoRlD"
      echo "${var@U}"
      echo "${var@u}"
      echo "${var@L}"

      var=("hElLo WoRlD")
      echo "${var@U}"
      echo "${var@u}"
      echo "${var@L}"
      echo "${var[*]@U}"
      echo "${var[*]@u}"
      echo "${var[*]@L}"
      echo "${var[@]@U}"
      echo "${var[@]@u}"
      echo "${var[@]@L}"

  - name: "Parameter quote transformations - Q"
    stdin: |
      var='""'
      echo "\${var@Q}: ${var@Q}"

      var="Hello"
      echo "\${var@Q}: ${var@Q}"

      var="Hello, world!"
      echo "\${var@Q}: ${var@Q}"

      var="a 'b c' d"
      echo "\${var@Q}: ${var@Q}"

      declare -a arr1=(a b c)
      echo "\${arr1@Q}: ${arr1@Q}"

      declare -A arr2=(["a"]=1 ["b"]=2)
      echo "\${arr2@Q}: ${arr2@Q}"

  - name: "Parameter quote transformations - K"
    known_failure: true # TODO: needs triage and debugging
    stdin: |
      var='""'
      echo "\${var@K}: ${var@K}"

      var="Hello"
      echo "\${var@K}: ${var@K}"

      var="Hello, world!"
      echo "\${var@K}: ${var@K}"

      var="a 'b c' d"
      echo "\${var@K}: ${var@K}"

      declare -a arr1=(a b c)
      echo "\${arr1@K}: ${arr1@K}"
      echo "\${arr1[1]@K}: ${arr1[1]@K}"
      echo "\${arr1[@]@K}: ${arr1[@]@K}"
      echo "\${arr1[*]@K}: ${arr1[*]@K}"

      declare -A arr2=(["a"]=1 ["b"]=2)
      echo "\${arr2@K}: ${arr2@K}"
      echo "\${arr2[b]@K}: ${arr2[b]@K}"
      echo "\${arr2[@]@K}: ${arr2[@]@K}"
      echo "\${arr2[*]@K}: ${arr2[*]@K}"

  - name: "Parameter quote transformations - k"
    min_oracle_version: 5.2
    stdin: |
      var='""'
      echo "\${var@k}: ${var@k}"

      var="Hello"
      echo "\${var@k}: ${var@k}"

      var="Hello, world!"
      echo "\${var@k}: ${var@k}"

      declare -a arr1=(a b c)
      echo "\${arr1@k}: ${arr1@k}"

      declare -A arr2=(["a"]=1 ["b"]=2)
      echo "\${arr2@k}: ${arr2@k}"

  - name: "Parameter transformations: expand escapes"
    stdin: |
      var="a\n\"b"
      echo "\${var@E}: ${var@E}"

  - name: "Parameter transformation: assignment"
    stdin: |
      var="hello"
      echo "\${var@A}: ${var@A}"

      declare -ia arr=(1 2 3)
      echo "\${arr@A}: ${arr@A}"
      echo "\${arr[1]@A}: ${arr[1]@A}"
      echo "\${arr[@]@A}: ${arr[@]@A}"
      echo "\${arr[*]@A}: ${arr[*]@A}"

      declare -A arr2=(["a"]=b)
      echo "\${arr2@A}: ${arr2@A}"
      echo "\${arr2[@]@A}: ${arr2[@]@A}"
      echo "\${arr2[*]@A}: ${arr2[*]@A}"

  - name: "Parameter transformation: attributes"
    stdin: |
      var="hello"
      echo "\${var@a}: ${var@a}"

      declare -ia arr=(1 2 3)
      echo "\${arr@a}: ${arr@a}"

      ref="arr"
      echo "\${!ref@a}: ${!ref@a}"

  - name: "Expansion with curly braces: commas"
    stdin: |
      echo "{a,b} =>"
      echo {a,b}
      echo

      echo "{a} =>"
      echo {a}
      echo

      echo "{a} =>"
      echo {a}
      echo

      echo "a{,}b =>"
      echo a{,}b
      echo

      echo "{} =>"
      echo {}
      echo

      echo "1{a,b}2 =>"
      echo 1{a,b}2
      echo

      echo "1{a,b}2{c,d}3 =>"
      echo 1{a,b}2{c,d}3
      echo

      echo "{a,b}{1,2} =>"
      echo {a,b}{1,2}
      echo

      echo 'W\{1,2}X =>'
      echo W\{1,2}X
      echo

      echo 'W{1,2\}X =>'
      echo W{1,2\}X
      echo

      echo '"W{1,2}X" =>'
      echo "W{1,2}X"
      echo

      var=value
      bar=other
      echo '{$var,$ba}r =>'
      echo {$va,$ba}r
      echo

      echo '\${a,b} =>'
      echo \${a,b}
      echo

  - name: "Expansion with curly braces: alpha ranges"
    stdin: |
      echo "{a..f} =>"
      echo {a..f}
      echo

      echo "{a..f..2} =>"
      echo {a..f..2}
      echo

  - name: "Expansion with curly braces: numeric ranges"
    stdin: |
      echo "{2..9} =>"
      echo {2..9}
      echo

      echo "{+2..+9} =>"
      echo {+2..+9}
      echo

      echo "{2..2} =>"
      echo {2..2}
      echo

      echo "{2..1} =>"
      echo {2..1}
      echo

      echo "{10..2..2} =>"
      echo {10..2..2}
      echo

      echo "{10..2..-2} =>"
      echo {10..2..-2}
      echo

      echo "{2..9..2} =>"
      echo {2..9..2}
      echo

      echo "{-1..-10} =>"
      echo {-1..-10}
      echo

      echo "{-10..-1} =>"
      echo {-10..-1}
      echo

  - name: "Expansion with curly braces: nested"
    stdin: |
      echo "W{{1..3},a,x}Y =>"
      echo W{{1..3},a,x}Y
      echo

  - name: "Iterate through modified array"
    stdin: |
      array=("aa" "ba" "ca")

      echo "With quotes:"
      for i in "${array[@]%a}"; do
          echo "Element: '$i'"
      done

      echo "Without quotes:"
      for i in ${array[@]%a}; do
          echo "Element: '$i'"
      done
