name: "CodeQL Advanced"

on:
  push:
    branches: ["main"]
  pull_request:
    branches: ["main"]
  schedule:
    - cron: "35 1 * * 3"

permissions: {}

jobs:
  analyze:
    name: Analyze (${{ matrix.language }})
    runs-on: ubuntu-latest
    permissions:
      # required for all workflows
      security-events: write
      # required to fetch internal or private CodeQL packs
      packages: read

    strategy:
      fail-fast: false
      matrix:
        include:
          - language: actions
            build-mode: none
          - language: python
            build-mode: none
          - language: rust
            build-mode: none
    steps:
      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          persist-credentials: false

      # Initializes the CodeQL tools for scanning.
      - name: Initialize CodeQL
        uses: github/codeql-action/init@76621b61decf072c1cee8dd1ce2d2a82d33c17ed # v3.29.5
        with:
          languages: ${{ matrix.language }}
          build-mode: ${{ matrix.build-mode }}
          queries: security-extended,security-and-quality

      # - name: Set up rust toolchain
      #   if: ${{ matrix.language == 'rust' }}
      #   uses: dtolnay/rust-toolchain@b3b07ba8b418998c39fb20f53e8b695cdcc8de1b # v1
      #   with:
      #     toolchain: stable

      # - name: Enable cargo cache
      #   if: ${{ matrix.language == 'rust' }}
      #   uses: Swatinem/rust-cache@98c8021b550208e191a6a3145459bfc9fb29c4c0 # v2.8.0

      # - name: Build Rust code
      #   if: ${{ matrix.language == 'rust' }}
      #   run: cargo build --all-targets --all-features

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@76621b61decf072c1cee8dd1ce2d2a82d33c17ed # v3.29.5
        with:
          category: "/language:${{matrix.language}}"
