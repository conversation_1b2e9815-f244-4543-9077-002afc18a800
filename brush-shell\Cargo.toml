[package]
name = "brush-shell"
description = "Rust-implemented shell focused on POSIX and bash compatibility"
version = "0.2.21"
authors.workspace = true
categories.workspace = true
edition.workspace = true
keywords.workspace = true
license.workspace = true
readme.workspace = true
repository.workspace = true
rust-version.workspace = true

[package.metadata.binstall]
pkg-url = "{ repo }/releases/download/{ name }-v{ version }/brush-{ target }{ archive-suffix }"

[[bin]]
name = "brush"
path = "src/main.rs"
bench = false

[[test]]
name = "brush-compat-tests"
path = "tests/compat_tests.rs"
harness = false

[[test]]
name = "brush-interactive-tests"
path = "tests/interactive_tests.rs"

[[test]]
name = "brush-completion-tests"
path = "tests/completion_tests.rs"

[features]
default = ["basic", "reedline", "minimal"]
basic = ["brush-interactive/basic"]
minimal = ["brush-interactive/minimal"]
reedline = ["brush-interactive/reedline"]

[lints]
workspace = true

[dependencies]
brush-core = { version = "^0.3.4", path = "../brush-core" }
clap = { version = "4.5.40", features = ["derive", "env", "wrap_help"] }
const_format = "0.2.34"
git-version = "0.3.9"
tracing = "0.1.41"
tracing-subscriber = "0.3.19"
human-panic = "2.0.3"

[target.'cfg(not(any(windows, unix)))'.dependencies]
brush-interactive = { version = "^0.2.21", path = "../brush-interactive", features = [
    "minimal",
] }
tokio = { version = "1.47.1", features = ["rt", "sync"] }

[target.'cfg(any(windows, unix))'.dependencies]
brush-interactive = { version = "^0.2.21", path = "../brush-interactive", features = [
    "basic",
    "reedline",
] }
crossterm = "0.29.0"
tokio = { version = "1.47.0", features = ["rt", "rt-multi-thread", "sync"] }

[target.wasm32-unknown-unknown.dependencies]
getrandom = { version = "0.3.3", features = ["wasm_js"] }
uuid = { version = "1.17.0", features = ["js"] }

[dev-dependencies]
anyhow = "1.0.98"
assert_cmd = "2.0.17"
assert_fs = "1.1.3"
colored = "3.0.0"
descape = "2.0.3"
diff = "0.1.13"
expectrl = { git = "https://github.com/zhiburt/expectrl", rev = "a0f4f7816b9a47a191dd858080e8fd80ff71cd96" }
glob = "0.3.2"
indent = "0.1.1"
junit-report = "0.8.3"
regex = "1.11.1"
serde = { version = "1.0.219", features = ["derive"] }
serde_yaml = "0.9.34"
strip-ansi-escapes = "0.2.1"
test-with = { version = "0.15.3", default-features = false, features = [
    "executable",
] }
version-compare = "0.2.0"
walkdir = "2.5.0"
