//! Command validation utilities for checking shell syntax and command existence.

use crate::shell::Shell;
use brush_parser::ast::{self, Command, SimpleCommand};

/// Result of command validation
#[derive(Debug, <PERSON>lone, PartialEq)]
pub enum ValidationResult {
    /// Input is valid and all commands exist
    Valid,
    /// Input has invalid shell syntax
    InvalidSyntax(String),
    /// Input has valid syntax but contains non-existent commands
    CommandNotFound(Vec<String>),
}

impl ValidationResult {
    /// Returns true if the validation passed (input is valid and all commands exist)
    pub fn is_valid(&self) -> bool {
        matches!(self, ValidationResult::Valid)
    }

    /// Returns true if the validation failed due to syntax or missing commands
    pub fn is_invalid(&self) -> bool {
        !self.is_valid()
    }
}

/// Validates user input for shell syntax and command existence
///
/// Only checks the top-level simple commands that the user directly inputs,
/// not commands inside scripts, functions, or control structures.
///
/// # Arguments
///
/// * `shell` - The shell instance to use for command lookup
/// * `input` - The user input string to validate
///
/// # Returns
///
/// A `ValidationResult` indicating whether the input is valid, has syntax errors,
/// or contains non-existent top-level commands.
pub fn validate_input(shell: &mut Shell, input: &str) -> ValidationResult {
    // First, check if the syntax is valid
    match shell.parse_string(input.to_owned()) {
        Ok(program) => {
            // Syntax is valid, now check only top-level simple commands
            let missing_commands = find_missing_top_level_commands(shell, &program);
            if missing_commands.is_empty() {
                ValidationResult::Valid
            } else {
                ValidationResult::CommandNotFound(missing_commands)
            }
        }
        Err(parse_error) => {
            // Check if it's an incomplete input (which should be allowed to continue)
            match parse_error {
                brush_parser::ParseError::Tokenizing { inner, position: _ }
                    if inner.is_incomplete() =>
                {
                    // Incomplete input is considered valid for continuation
                    ValidationResult::Valid
                }
                brush_parser::ParseError::ParsingAtEndOfInput => {
                    // For user input validation, we should be more strict
                    // Check if the input ends with operators that require continuation
                    let trimmed_input = input.trim();
                    if trimmed_input.ends_with('|')
                        || trimmed_input.ends_with("&&")
                        || trimmed_input.ends_with("||")
                        || trimmed_input.ends_with('&')
                        || trimmed_input.ends_with(';')
                    {
                        // These are syntax errors, not incomplete input
                        ValidationResult::InvalidSyntax("Incomplete command".to_string())
                    } else {
                        // Other cases might be legitimate incomplete input (like "if true; then")
                        ValidationResult::Valid
                    }
                }
                _ => {
                    // Real syntax error
                    ValidationResult::InvalidSyntax(parse_error.to_string())
                }
            }
        }
    }
}

/// Finds missing top-level simple commands in the given program AST
///
/// Only checks simple commands at the top level, ignoring commands inside
/// control structures, functions, or other compound commands.
///
/// # Arguments
///
/// * `shell` - The shell instance to use for command lookup
/// * `program` - The parsed program AST to check
///
/// # Returns
///
/// A vector of command names that were not found
fn find_missing_top_level_commands(shell: &mut Shell, program: &ast::Program) -> Vec<String> {
    let mut missing_commands = Vec::new();
    let mut visited_commands = std::collections::HashSet::new();

    for complete_command in &program.complete_commands {
        find_missing_top_level_commands_in_compound_list(
            shell,
            complete_command,
            &mut missing_commands,
            &mut visited_commands,
        );
    }

    missing_commands
}

/// Finds missing top-level commands in a compound list
fn find_missing_top_level_commands_in_compound_list(
    shell: &mut Shell,
    compound_list: &ast::CompoundList,
    missing_commands: &mut Vec<String>,
    visited_commands: &mut std::collections::HashSet<String>,
) {
    for compound_list_item in &compound_list.0 {
        find_missing_top_level_commands_in_compound_list_item(
            shell,
            compound_list_item,
            missing_commands,
            visited_commands,
        );
    }
}

/// Finds missing top-level commands in a compound list item
fn find_missing_top_level_commands_in_compound_list_item(
    shell: &mut Shell,
    compound_list_item: &ast::CompoundListItem,
    missing_commands: &mut Vec<String>,
    visited_commands: &mut std::collections::HashSet<String>,
) {
    // CompoundListItem is (AndOrList, SeparatorOperator)
    find_missing_top_level_commands_in_and_or(
        shell,
        &compound_list_item.0,
        missing_commands,
        visited_commands,
    );
}

/// Finds missing top-level commands in an and-or command
fn find_missing_top_level_commands_in_and_or(
    shell: &mut Shell,
    and_or: &ast::AndOrList,
    missing_commands: &mut Vec<String>,
    visited_commands: &mut std::collections::HashSet<String>,
) {
    find_missing_top_level_commands_in_pipeline(
        shell,
        &and_or.first,
        missing_commands,
        visited_commands,
    );

    for additional_item in &and_or.additional {
        let pipeline = match additional_item {
            ast::AndOr::And(pipeline) => pipeline,
            ast::AndOr::Or(pipeline) => pipeline,
        };
        find_missing_top_level_commands_in_pipeline(
            shell,
            pipeline,
            missing_commands,
            visited_commands,
        );
    }
}

/// Finds missing top-level commands in a pipeline
fn find_missing_top_level_commands_in_pipeline(
    shell: &mut Shell,
    pipeline: &ast::Pipeline,
    missing_commands: &mut Vec<String>,
    visited_commands: &mut std::collections::HashSet<String>,
) {
    for command in &pipeline.seq {
        find_missing_top_level_commands_in_command(
            shell,
            command,
            missing_commands,
            visited_commands,
        );
    }
}

/// Finds missing top-level commands in a command
/// Only checks simple commands, ignoring compound commands (control structures)
fn find_missing_top_level_commands_in_command(
    shell: &mut Shell,
    command: &Command,
    missing_commands: &mut Vec<String>,
    visited_commands: &mut std::collections::HashSet<String>,
) {
    match command {
        Command::Simple(simple_command) => {
            // Only check simple commands at the top level
            check_simple_command(shell, simple_command, missing_commands, visited_commands);
        }
        Command::Compound(_compound_command, _redirects) => {
            // Ignore compound commands (if, for, while, etc.) - don't check commands inside them
            // This is the key difference: we don't recurse into compound commands
        }
        Command::Function(_function_def) => {
            // Function definitions don't execute commands immediately
        }
        Command::ExtendedTest(_test_expr) => {
            // Extended test expressions don't execute external commands
        }
    }
}

/// Checks if a simple command exists
fn check_simple_command(
    shell: &mut Shell,
    simple_command: &SimpleCommand,
    missing_commands: &mut Vec<String>,
    visited_commands: &mut std::collections::HashSet<String>,
) {
    if let Some(word) = &simple_command.word_or_name {
        let command_name = word.to_string();

        // Skip if we've already checked this command
        if visited_commands.contains(&command_name) {
            return;
        }
        visited_commands.insert(command_name.clone());

        // Check if it's a builtin command
        if shell.builtins.contains_key(&command_name) {
            return;
        }

        // Check if it's a function
        if shell.funcs.get(&command_name).is_some() {
            return;
        }

        // Check if it's an executable in PATH
        if command_name.contains(std::path::MAIN_SEPARATOR) {
            // It's a path, check if the file exists and is executable
            let path = shell.get_absolute_path(std::path::Path::new(&command_name));
            if !path.exists() || !crate::sys::fs::PathExt::executable(path.as_path()) {
                missing_commands.push(command_name);
            }
        } else {
            // It's a command name, search in PATH
            let found_in_path = shell.find_first_executable_in_path(&command_name);

            // Additional check: if we found a path, make sure it actually exists
            let command_exists = if let Some(ref path) = found_in_path {
                path.exists()
            } else {
                false
            };

            if !command_exists {
                missing_commands.push(command_name);
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::{ValidationResult, validate_input};
    use crate::shell::{CreateOptions, Shell};

    async fn create_test_shell() -> Shell {
        Shell::new(&CreateOptions::default()).await.unwrap()
    }

    #[tokio::test]
    async fn test_valid_builtin_command() {
        let mut shell = create_test_shell().await;
        let result = validate_input(&mut shell, "echo hello");
        assert!(result.is_valid());
    }

    #[tokio::test]
    async fn test_invalid_syntax() {
        let mut shell = create_test_shell().await;
        let result = validate_input(&mut shell, "echo hello |");
        match result {
            ValidationResult::InvalidSyntax(_) => {
                // This is expected
            }
            _ => panic!("Expected InvalidSyntax, got {:?}", result),
        }
    }

    #[tokio::test]
    async fn test_nonexistent_command() {
        let mut shell = create_test_shell().await;

        // Use a command name that definitely doesn't exist
        let nonexistent_cmd = "this_command_definitely_does_not_exist_anywhere_12345";

        // Clear any potential cache entries
        shell.program_location_cache.unset(nonexistent_cmd);

        let result = validate_input(&mut shell, nonexistent_cmd);
        match result {
            ValidationResult::CommandNotFound(commands) => {
                assert!(commands.contains(&nonexistent_cmd.to_string()));
            }
            _ => panic!("Expected CommandNotFound, got {:?}", result),
        }
    }

    #[tokio::test]
    async fn test_top_level_simple_command_validation() {
        let mut shell = create_test_shell().await;

        // Test valid top-level command
        let result = validate_input(&mut shell, "echo hello");
        assert!(result.is_valid());

        // Test invalid top-level command
        let result = validate_input(&mut shell, "invalid_top_level_cmd");
        match result {
            ValidationResult::CommandNotFound(commands) => {
                assert!(commands.contains(&"invalid_top_level_cmd".to_string()));
            }
            _ => panic!("Expected CommandNotFound, got {:?}", result),
        }
    }

    #[tokio::test]
    async fn test_compound_commands_are_always_valid() {
        let mut shell = create_test_shell().await;

        // All these should be valid because they are compound commands
        let test_cases = vec![
            "if invalid_cmd; then echo hello; fi",
            "for i in 1 2 3; do invalid_cmd; done",
            "while invalid_cmd; do echo hello; done",
            "{ invalid_cmd; echo hello; }",
            "(invalid_cmd)",
            "case $var in *) invalid_cmd ;; esac",
        ];

        for test_case in test_cases {
            let result = validate_input(&mut shell, test_case);
            assert!(
                result.is_valid(),
                "Compound command should be valid: {}",
                test_case
            );
        }
    }
}
