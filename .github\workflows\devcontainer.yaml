name: "Devcontainer"
on:
  push:
    branches:
      - main
  pull_request:
    paths:
      - ".devcontainer/**"

permissions: {}

jobs:
  build:
    name: "Build devcontainer"
    runs-on: ubuntu-24.04
    permissions:
      contents: read
      packages: read
    steps:
      - name: Checkout sources
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          persist-credentials: false

      - name: Login to GitHub Container Registry
        uses: docker/login-action@184bdaa0721073962dff0199f1fb9940f07167d1 # v3.5.0
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Pre-build dev container image
        uses: devcontainers/ci@8bf61b26e9c3a98f69cb6ce2f88d24ff59b785c6 # v0.3.1900000417
        with:
          imageName: ghcr.io/reubeno/brush/devcontainer
          imageTag: latest
          cacheFrom: ghcr.io/reubeno/brush/devcontainer
          push: never

  build_and_publish:
    name: "Build and publish devcontainer"
    runs-on: ubuntu-24.04
    permissions:
      contents: read
      packages: write
    steps:
      - name: Checkout sources
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          persist-credentials: false

      - name: Login to GitHub Container Registry
        uses: docker/login-action@184bdaa0721073962dff0199f1fb9940f07167d1 # v3.5.0
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Pre-build dev container image
        uses: devcontainers/ci@8bf61b26e9c3a98f69cb6ce2f88d24ff59b785c6 # v0.3.1900000417
        with:
          imageName: ghcr.io/reubeno/brush/devcontainer
          imageTag: latest
          cacheFrom: ghcr.io/reubeno/brush/devcontainer
          push: filter
          refFilterForPush: refs/heads/main
          eventFilterForPush: push
