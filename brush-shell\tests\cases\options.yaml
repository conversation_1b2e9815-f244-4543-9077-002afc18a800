name: "Options"
cases:
  - name: "Default options"
    stdin: |
      echo "Default options: $-"

  - name: "set -a"
    stdin: |
      v1=1 v2=2 v3=3 v4=4 v5=(a b c)
      declare -p v1 v2 v3 v4

      set -a
      v1=reassigned
      v2+=appended
      declare -i v3
      v4+=(appended)
      v5[2]=updated
      v6=new
      v7[0]=new
      v8=(new)
      declare v9=new
      declare -a v10=(new)

      declare -p v1 v2 v3 v4 v5 v6 v7 v8 v9 v10

  - name: "set -n"
    stdin: |
      set -n
      touch somefile.txt
      ls

  - name: "set -B"
    stdin: |
      set +B
      echo "+B: " ${a,b}

      set -B
      echo "-B: " ${a,b}

  - name: "set -t"
    args: ["-t"]
    stdin: |
      echo first
      echo second

  - name: "set -C"
    ignore_stderr: true
    stdin: |
      touch existing-file

      set -C

      echo hi > non-existing-file
      echo "Result (non existing): $?"
      echo "File contents: $(cat non-existing-file)"
      echo

      echo hi > /dev/null
      echo "Result (device file): $?"
      echo

      echo hi > existing-file
      echo "Result (existing file): $?"
      echo "File contents: $(cat existing-file)"
      echo

      echo hi >| existing-file
      echo "Result (clobber): $?"
      echo "File contents: $(cat existing-file)"
      echo

  - name: "set -x"
    stdin: |
      set -x

      ls

      for f in 1 2 3; do echo ${f}; done

      case 1 in
        1) echo "1";;
        *) echo "not";;
      esac

      while false; do
        echo body
      done

      newvar=$(echo "new")

      x=$((3 + 7))

      if [[ x == 10 && ! 0 ]]; then
        echo "Math checks"
      fi

      var="x"
      [[ ${var} && ${var//[[:space:]]/} ]]

      for ((i = 0; i < 3; i++)); do
        echo $i
      done

      ((x = 3)) || ((x = 4))

      override=value echo some_output
