---
source: brush-parser/src/word.rs
expression: "super::parse_brace_expansions(input,\n&options)?.ok_or_else(||\nanyhow::anyhow!(\"Expected brace expansion to be parsed successfully\"))?"
---
[
  Expr([
    Child([
      Text("a"),
    ]),
    Child([
      Text("b"),
      Expr([
        Child([
          Text("1"),
        ]),
        Child([
          Text("2"),
        ]),
      ]),
    ]),
  ]),
]
