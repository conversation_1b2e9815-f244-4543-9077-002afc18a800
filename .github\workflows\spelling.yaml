name: Spelling
on: [pull_request]

env:
  RUST_BACKTRACE: 1
  CARGO_TERM_COLOR: always
  CLICOLOR: 1
  CLICOLOR_FORCE: 1

permissions:
  contents: read

jobs:
  spelling:
    name: spell-check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout brush
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          persist-credentials: false

      - name: Spell check repo
        uses: crate-ci/typos@52bd719c2c91f9d676e2aa359fc8e0db8925e6d8 # v1.35.3
