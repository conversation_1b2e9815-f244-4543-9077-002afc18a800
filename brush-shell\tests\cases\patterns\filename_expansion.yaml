name: "Filename expansion"
cases:
  - name: "Single file expansion"
    stdin: "echo file1.txt"

  - name: "Expansion with noglob"
    test_files:
      - path: "file1.txt"
      - path: "file2.txt"
    stdin: |
      set -f
      echo "*.txt:" *.txt

  - name: "Multiple file expansion"
    test_files:
      - path: "file1.txt"
      - path: "file2.txt"
    stdin: |
      echo *.txt
      echo *."txt"

  - name: "File expansion with nocaseglob"
    test_files:
      - path: "FILE1.TXT"
      - path: "file2.txt"
    stdin: |
      shopt -s nocaseglob
      echo "*.txt:" *.txt
      echo "*.TXT:" *.txt

  - name: "Nested directory expansion"
    test_files:
      - path: "dir/file1.txt"
    stdin: "echo dir/*"

  - name: "Multiple level directory expansion"
    test_files:
      - path: "dir/subdir/file1.txt"
    stdin: "echo dir/subdir/*.txt"

  - name: "Expansion with no matches"
    test_files:
      - path: "file1.txt"
    stdin: "echo *.jpg"

  - name: "Expansion with no matches + nullglob"
    test_files:
      - path: "file1.txt"
    stdin: |
      shopt -s nullglob
      echo "*.jpg:" *.jpg

  - name: "Expansion with special characters"
    test_files:
      - path: "file1.txt"
      - path: "file2.txt"
    stdin: "echo file?.txt"

  - name: "Expansion with brackets"
    test_files:
      - path: "file1.txt"
      - path: "file2.txt"
    stdin: "echo file[12].txt"

  - name: "Expansion with range"
    test_files:
      - path: "file1.txt"
      - path: "file2.txt"
    stdin: "echo file[1-2].txt"

  - name: "Expansion with negation"
    test_files:
      - path: "file1.txt"
      - path: "file2.txt"
    stdin: "echo file[!2].txt"

  - name: "Expansion with tilde"
    skip: true # Started failing with 5.3 on macOS
    stdin: |
      HOME=/some/dir

      echo ~/file1.txt
      echo ~/file1.txt:~/file1.txt

  - name: "Expansion with dots"
    stdin: |
      echo ./file1.txt
      echo ../file1.txt

  - name: "Expansion with mixed patterns"
    test_files:
      - path: "dir/subdir/file1.txt"
    stdin: "echo dir/*/file?.txt"

  - name: "Expansion with escaped characters"
    test_files:
      - path: "file*.txt"
    stdin: "echo file\\*.txt"

  - name: "Pathname expansion: extglob disabled"
    ignore_stderr: true
    test_files:
      - path: "ab.txt"
      - path: "abc.txt"
      - path: "abd.txt"
      - path: "def.txt"
      - path: "abadac.txt"
      - path: "fabadac.txt"
      - path: "f.txt"
      - path: "script.sh"
        contents: |
          echo !(a*)
          echo "result: $?"

          echo @(abc|abd).txt
          echo "result: $?"

          echo ab?(c).txt
          echo "result: $?"

          echo *(ab|ad|ac).txt
          echo "result: $?"

          echo f+(ab|ad|ac).txt
          echo "result: $?"
    stdin: |
      shopt -u extglob
      chmod +x ./script.sh
      ./script.sh

  - name: "Pathname expansion: Inverted patterns"
    ignore_stderr: true
    test_files:
      - path: "abc.txt"
      - path: "abd.txt"
      - path: "def.txt"
    stdin: |
      shopt -s extglob
      echo "1: " !(a*)
      echo "2: " !(abc.txt)
      echo "3: " !(abc)
      echo "4: " !(*)

  - name: "Pathname expansion: Degenerate inverted pattern"
    test_files:
      - path: "abc.txt"
      - path: "abd.txt"
      - path: "def.txt"
    stdin: |
      shopt -s extglob
      echo !()

  - name: "Pathname expansion: Extended patterns"
    ignore_stderr: true
    test_files:
      - path: "abc.txt"
      - path: "abd.txt"
    stdin: |
      shopt -s extglob
      echo "1: " @(abc|abd).txt
      echo "2: " @(abc.txt)
      echo "3: " @(abc)
      echo "4: " @(|abc.txt)

  - name: "Pathname expansion: Optional patterns"
    ignore_stderr: true
    test_files:
      - path: "ab.txt"
      - path: "abc.txt"
    stdin: |
      shopt -s extglob
      echo ab?(c).txt

  - name: "Pathname expansion: Star patterns"
    ignore_stderr: true
    test_files:
      - path: "abadac.txt"
      - path: "ab.txt"
    stdin: |
      shopt -s extglob
      echo *(ab|ad|ac).txt

  - name: "Pathname expansion: Plus patterns"
    ignore_stderr: true
    test_files:
      - path: "fabadac.txt"
      - path: "f.txt"
    stdin: |
      shopt -s extglob
      echo f+(ab|ad|ac).txt

  - name: "Pathname expansion: quoting"
    test_files:
      - path: test.txt
      - path: subdir/test.txt
    stdin: |
      echo "./test"*.txt
      echo "subdir"/*.txt
      echo "test.*"

  - name: "Pathname expansion: dot files (no dotglob)"
    min_oracle_version: 5.2
    stdin: |
      touch .file
      touch .dir

      shopt -u dotglob
      echo "*   : " *
      echo "*i* : " *i*
      echo "./* : " ./*
      echo ".*  : " .*
      echo "./.*: " ./.*

  - name: "Pathname expansion: dot files (with dotglob)"
    min_oracle_version: 5.2
    stdin: |
      touch .file
      touch .dir

      shopt -s dotglob
      echo "*   : " *
      echo "*i* : " *i*
      echo "./* : " ./*
      echo ".*  : " .*
      echo "./.*: " ./.*
