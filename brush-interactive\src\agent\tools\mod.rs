//! Tools for the AI Agent

pub mod advanced_tools;
pub mod basic_tools;
pub mod web_tools;

use crate::agent::AgentResult;
use serde_json::Value;
use std::collections::HashMap;

/// Trait for implementing agent tools
pub trait Tool: Send + Sync {
    /// Get the name of the tool
    fn name(&self) -> &str;

    /// Get the description of the tool
    fn description(&self) -> &str;

    /// Execute the tool with given parameters
    fn execute(&self, parameters: HashMap<String, String>) -> AgentResult<ToolResult>;
}

/// Result of tool execution
#[derive(Debug, Clone)]
pub struct ToolResult {
    /// Status code (similar to HTTP status codes)
    pub status: u16,
    /// Human-readable message
    pub message: String,
    /// Optional data payload
    pub data: Option<Value>,
}

impl ToolResult {
    /// Create a successful result
    pub fn success(message: String) -> Self {
        Self {
            status: 200,
            message,
            data: None,
        }
    }

    /// Create a successful result with data
    pub fn success_with_data(message: String, data: Value) -> Self {
        Self {
            status: 200,
            message,
            data: Some(data),
        }
    }

    /// Create an error result
    pub fn error(status: u16, message: String) -> Self {
        Self {
            status,
            message,
            data: None,
        }
    }

    /// Create a confirmation required result
    pub fn confirmation_required(message: String, data: Value) -> Self {
        Self {
            status: 202,
            message,
            data: Some(data),
        }
    }

    /// Convert to JSON string
    pub fn to_json(&self) -> String {
        serde_json::json!({
            "status": self.status,
            "message": self.message,
            "data": self.data
        })
        .to_string()
    }

    /// Check if the result indicates success
    pub fn is_success(&self) -> bool {
        self.status == 200
    }

    /// Check if the result requires confirmation
    pub fn requires_confirmation(&self) -> bool {
        self.status == 202
    }
}
