---
source: brush-parser/src/tokenizer.rs
expression: "test_tokenizer(r\"1\\ 2 3\")?"
---
TokenizerResult(
  input: "1\\ 2 3",
  result: [
    W("1\\ 2", Loc(
      start: Pos(
        idx: 0,
        line: 1,
        col: 1,
      ),
      end: Pos(
        idx: 4,
        line: 1,
        col: 5,
      ),
    )),
    W("3", Loc(
      start: Pos(
        idx: 5,
        line: 1,
        col: 6,
      ),
      end: Pos(
        idx: 6,
        line: 1,
        col: 7,
      ),
    )),
  ],
)
