name: "Builtins: trap"
cases:
  - name: "trap registration"
    stdin: |
      trap "echo 1" SIGINT
      trap "echo 2" SIGINT
      trap -p INT

      trap "echo 3" int
      trap -p INT

      trap "echo 4" 2
      trap -p INT

  - name: "trap unregistering"
    stdin: |
      echo "[Case 1]"
      trap "echo 1" SIGINT
      trap SIGINT
      trap -p INT

      echo "[Case 2]"
      trap "echo 2" SIGINT
      trap - SIGINT
      trap -p INT

  - name: "trap EXIT"
    known_failure: true # TODO: needs triage and debugging
    stdin: |
      trap "echo [exit]" EXIT
      trap -p EXIT

  - name: "trap DEBUG"
    stdin: |
      trap 'echo [command: ${BASH_COMMAND}]' DEBUG
      trap -p DEBUG

  - name: "trap ERR"
    stdin: |
      trap "echo [err]" ERR
      trap -p ERR
