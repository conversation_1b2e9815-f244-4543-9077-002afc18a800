name: "Compound commands: arithmetic"
cases:
  - name: "Basic arithmetic statements"
    stdin: |
      ((0 == 0)) && echo "0 == 0"
      ((0 != 0)) && echo "0 != 0"

  - name: "Arithmetic statements with parens"
    stdin: |
      (( (0) )) && echo "0"
      (( (1) )) && echo "1"

  - name: "Arithmetic statements with parens and operators"
    stdin: |
      (( (0) == 0 )) && echo "0 == 0"
      (( (1) != 0 )) && echo "1 != 0"
