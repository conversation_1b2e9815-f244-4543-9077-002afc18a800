name: "Prompt"
cases:
  - name: "Simple prompts"
    stdin: |
      prompt='$ '
      echo "Prompt: '${prompt@P}'"

      prompt='a\r\n> '
      echo "Prompt: '${prompt@P}'"

      prompt='\\'
      echo "Prompt: '${prompt@P}'"

      prompt='\81'
      echo "Prompt: '${prompt@P}'"

  - name: "Working dir based prompts"
    stdin: |
      cd /usr

      prompt='\w '
      echo "Prompt: '${prompt@P}'"

      prompt='\W '
      echo "Prompt: '${prompt@P}'"

      arr=("\w" "\w")
      echo "Prompt: '${arr@P}'"
      echo "Prompt: '${arr[@]@P}'"
      echo "Prompt: '${arr[*]@P}'"

  - name: "Non-printing chars"
    stdin: |
      prompt='\[\]Prompt>\[\]'
      echo "Prompt: '${prompt@P}'"

  - name: "Hostname in prompts"
    stdin: |
      prompt='\h '
      echo "Prompt: '${prompt@P}'"

      prompt='\H '
      echo "Prompt: '${prompt@P}'"

  - name: "Shell name"
    stdin: |
      prompt='\s'
      [[ "${prompt@P}" == "$(basename $0)" ]]

  - name: "Shell version info"
    stdin: |
      prompt='\v'
      [[ "${prompt@P}" =~ ^\d+\.\d+$ ]] && echo "Version is well-formatted"

      prompt='\V'
      [[ "${prompt@P}" == ^\d+\.\d+\.\d+$ ]] && echo "Release is correct"

  - name: "Simple date"
    stdin: |
      prompt='\d'
      [[ "${prompt@P}" == $(date +'%a %b %d') ]] && echo '\d date matches'

  - name: "Date format with plain string"
    stdin: |
      prompt='\D{something}'
      echo "Date format with plain string: '${prompt@P}'"

  - name: "Date format with year"
    stdin: |
      prompt='\d{%Y}'
      echo "Date format with year: '${prompt@P}'"

  - name: "Time format: A"
    stdin: |
      prompt='\A'
      expanded=${prompt@P}
      roundtripped=$(date --date="${expanded}" +'%H:%M')
      [[ ${expanded} == ${roundtripped} ]] && echo "Time matches"

  - name: "Time format: @"
    stdin: |
      prompt='\@'
      expanded=${prompt@P}
      roundtripped=$(date --date="${expanded}" +'%I:%M %p')
      [[ ${expanded} == ${roundtripped} ]] && echo "Time matches"

  - name: "Time format: T"
    stdin: |
      prompt='\T'
      expanded=${prompt@P}
      roundtripped=$(date --date="${expanded}" +'%I:%M:%S')
      [[ ${expanded} == ${roundtripped} ]] && echo "Time matches"

  - name: "Time format: t"
    stdin: |
      prompt='\t'
      expanded=${prompt@P}
      roundtripped=$(date --date="${expanded}" +'%H:%M:%S')
      [[ ${expanded} == ${roundtripped} ]] && echo "Time matches"

  - name: "Current history number"
    known_failure: true
    stdin: |
      prompt='\!'
      expanded=${prompt@P}
      roundtripped=$(history | tail -n 1 | cut -d ' ' -f 1)
      [[ ${expanded} == ${roundtripped} ]] && echo "History number matches"

  - name: "Current command number"
    known_failure: true # Issue #471
    stdin: |
      prompt='\#'
      expanded=${prompt@P}
      echo "${expanded}"

  - name: "Command substitution in prompt"
    known_failure: true # @P expansion doesn't word-expand after prompt expanding
    stdin: |
      prompt='$(echo Hello)'
      echo "Without spaces: ${prompt@P}"

      prompt=' $(echo Hello) '
      echo "With spaces: ${prompt@P}"

  - name: "Backquoted command substitution in prompt"
    known_failure: true # @P expansion doesn't word-expand after prompt expanding
    stdin: |
      prompt='`echo Hello`'
      echo "Without spaces: ${prompt@P}"

      prompt=' `echo Hello` '
      echo "With spaces: ${prompt@P}"
