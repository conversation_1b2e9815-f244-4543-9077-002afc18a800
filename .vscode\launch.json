{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug executable 'brush'",
            "cargo": {
                "args": [
                    "build",
                    "--bin=brush",
                    "--package=brush-shell"
                ],
                "filter": {
                    "name": "brush",
                    "kind": "bin"
                }
            },
            "args": [
                "--enable-highlighting"
            ],
            "cwd": "${workspaceFolder}"
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug integration test 'brush-compat-tests'",
            "cargo": {
                "args": [
                    "test",
                    "--no-run",
                    "--test=brush-compat-tests",
                    "--package=brush-shell"
                ],
                "filter": {
                    "name": "brush-compat-tests",
                    "kind": "test"
                }
            },
            "args": [],
            "cwd": "${workspaceFolder}"
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug executable 'xtask'",
            "cargo": {
                "args": [
                    "build",
                    "--bin=xtask",
                    "--package=xtask"
                ],
                "filter": {
                    "name": "xtask",
                    "kind": "bin"
                }
            },
            "args": [],
            "cwd": "${workspaceFolder}"
        }
    ]
}