[package]
name = "brush-interactive"
description = "Interactive layer of brush-shell"
version = "0.2.21"
authors.workspace = true
categories.workspace = true
edition.workspace = true
keywords.workspace = true
license.workspace = true
readme.workspace = true
repository.workspace = true
rust-version.workspace = true

[lib]
bench = false

[features]
default = []
basic = ["dep:crossterm"]
minimal = []
reedline = ["dep:reedline", "dep:nu-ansi-term"]

[lints]
workspace = true

[dependencies]
brush-parser = { version = "^0.2.19", path = "../brush-parser" }
brush-core = { version = "^0.3.4", path = "../brush-core" }
crossterm = { version = "0.29.0", features = ["serde"], optional = true }
indexmap = "2.10.0"
nu-ansi-term = { version = "0.50.1", optional = true }
reedline = { version = "0.41.0", optional = true }
thiserror = "2.0.12"
tracing = "0.1.41"

[target.'cfg(any(windows, unix))'.dependencies]
tokio = { version = "1.47.1", features = ["macros", "signal"] }

[target.wasm32-unknown-unknown.dependencies]
getrandom = { version = "0.3.3", features = ["wasm_js"] }
