---
source: brush-parser/src/tokenizer.rs
expression: "test_tokenizer(r\"cat <<HERE\nSOMETHING\nHERE\necho after\n\")?"
---
TokenizerResult(
  input: "cat <<HERE\nSOMETHING\nHERE\necho after\n",
  result: [
    W("cat", Loc(
      start: Pos(
        idx: 0,
        line: 1,
        col: 1,
      ),
      end: Pos(
        idx: 3,
        line: 1,
        col: 4,
      ),
    )),
    Op("<<", Loc(
      start: Pos(
        idx: 4,
        line: 1,
        col: 5,
      ),
      end: Pos(
        idx: 6,
        line: 1,
        col: 7,
      ),
    )),
    W("HERE", Loc(
      start: Pos(
        idx: 6,
        line: 1,
        col: 7,
      ),
      end: Pos(
        idx: 10,
        line: 1,
        col: 11,
      ),
    )),
    W("SOMETHING\n", Loc(
      start: Pos(
        idx: 11,
        line: 2,
        col: 1,
      ),
      end: Pos(
        idx: 26,
        line: 4,
        col: 1,
      ),
    )),
    W("HERE", Loc(
      start: Pos(
        idx: 26,
        line: 4,
        col: 1,
      ),
      end: Pos(
        idx: 26,
        line: 4,
        col: 1,
      ),
    )),
    Op("\n", Loc(
      start: Pos(
        idx: 10,
        line: 1,
        col: 11,
      ),
      end: Pos(
        idx: 11,
        line: 2,
        col: 1,
      ),
    )),
    W("echo", Loc(
      start: Pos(
        idx: 26,
        line: 4,
        col: 1,
      ),
      end: Pos(
        idx: 30,
        line: 4,
        col: 5,
      ),
    )),
    W("after", Loc(
      start: Pos(
        idx: 31,
        line: 4,
        col: 6,
      ),
      end: Pos(
        idx: 36,
        line: 4,
        col: 11,
      ),
    )),
    Op("\n", Loc(
      start: Pos(
        idx: 36,
        line: 4,
        col: 11,
      ),
      end: Pos(
        idx: 37,
        line: 5,
        col: 1,
      ),
    )),
  ],
)
