name: "Compound commands: for"
cases:
  - name: "Single-line for loop"
    stdin: |
      for f in 1 2 3; do echo $f; done

  - name: "Multi-line for loop"
    stdin: |
      for f in 1 2 3; do
        echo $f
      done

  - name: "Empty for loop"
    stdin: |
      for f in; do echo $f; done

  - name: "for loop without in"
    stdin: |
      set -- a b c

      echo "Loop 1"
      for f; do echo $f; done

      echo "Loop 2: no semicolon"
      for f do echo $f; done

  - name: "for loop without in but spaces"
    stdin: |
      set -- a "b c" d

      echo "Loop 1"
      for f; do echo $f; done

      echo "Loop 2: no semicolon"
      for f do echo $f; done

  - name: "Break in for loop"
    stdin: |
      for f in 1 2 3; do
        echo $f
        break
      done

  - name: "Break in nested for loop"
    stdin: |
      for f in 1 2 3; do
        for g in a b c; do
          echo "f=$f g=$g"
          true && break
        done
        echo "Left inner loop"
      done

  - name: "Break 1 in nested for loops"
    stdin: |
      for f in 1 2 3; do
        for g in a b c; do
          echo "f=$f g=$g"
          break 1
        done
      done

  - name: "Break 2 in nested for loops"
    stdin: |
      for f in 1 2 3; do
        for g in a b c; do
          echo "f=$f g=$g"
          break 2
        done
      done

  - name: "Break out of nested for/while loops"
    stdin: |
      for f in 1 2 3; do
        while true; do
          echo "f=$f"
          break 2
        done
      done

  - name: "Continue in for loop"
    stdin: |
      for f in 1 2 3; do
        echo $f
        continue
        echo $f
      done

  - name: "Continue with bad N"
    known_failure: true # Fails differently
    stdin: |
      for f in 1 2 3; do
        echo $f
        continue 0
        echo result: $?
      done

  - name: "Continue in nested for loop"
    stdin: |
      for f in a b c; do
        for g in 1 2 3; do
          echo "f=$f g=$g"
          continue
          echo "Should not print"
        done
        echo "Left inner loop"
      done

  - name: "Continue in nested for loop with explicit N=1"
    stdin: |
      for f in a b c; do
        for g in 1 2 3; do
          echo "f=$f g=$g"
          continue 1
          echo "Should not print"
        done
        echo "Left inner loop"
      done

  - name: "Continue in nested for loop with explicit N=2"
    stdin: |
      for f in a b c; do
        for g in 1 2 3; do
          echo "f=$f g=$g"
          continue 2
          echo "Should not print"
        done
        echo "Left inner loop"
      done

  - name: "Continue in nested for loop with too large N"
    known_failure: true
    stdin: |
      for f in a b c; do
        for g in 1 2 3; do
          echo "f=$f g=$g"
          continue 3
          echo "Should not print"
        done
        echo "Left inner loop"
      done

  - name: "Multi-line for loop"
    test_files:
      - path: "script.sh"
        contents: |
          for f in 1 2 3; do
            echo $f
          done
    args: ["./script.sh"]

  - name: "For loop piped"
    stdin: |
      for f in ab ac bd ef; do echo $f; done | grep b

  - name: "For loop output redirection"
    stdin: |
      for f in a b c; do echo $f; done > ./out.txt

  - name: "For reserved word handling"
    stdin: |
      for for in for; do echo $for; done
