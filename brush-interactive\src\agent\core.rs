//! Core AI Agent implementation

use crate::agent::conversation_manager::Message;
use crate::agent::tool_executor::ExecutionResult;
use crate::agent::{Agent<PERSON>onfig, Agent<PERSON><PERSON><PERSON>, Agent<PERSON><PERSON><PERSON>, Conversation<PERSON><PERSON>ger, ToolExecutor};
use reqwest::Client;
use serde_json::{Value, json};
use std::time::Duration;

/// Main AI Agent that processes user input through AI models and tools
pub struct Agent {
    /// Configuration for the agent
    config: AgentConfig,
    /// HTTP client for API calls
    client: Client,
    /// Conversation manager
    conversation_manager: ConversationManager,
    /// Tool executor
    tool_executor: ToolExecutor,
    /// Maximum number of conversation turns
    max_turns: usize,
}

/// Result of processing user input
#[derive(Debug, Clone)]
pub struct ProcessResult {
    /// Final response to the user
    pub response: String,
    /// Whether the task was completed
    pub task_completed: bool,
    /// Whether user input is required
    pub requires_user_input: bool,
    /// Number of turns taken
    pub turns_taken: usize,
}

impl Agent {
    /// Create a new AI Agent
    pub fn new(config: AgentConfig) -> AgentResult<Self> {
        let client = Client::builder()
            .timeout(Duration::from_secs(config.openai.timeout_seconds))
            .build()?;

        let conversation_manager = ConversationManager::new(config.clone())?;
        let tool_executor = ToolExecutor::new(config.clone())?;

        Ok(Self {
            config,
            client,
            conversation_manager,
            tool_executor,
            max_turns: 10, // Prevent infinite loops
        })
    }

    /// Process user input through the AI agent
    pub async fn process_user_input(&mut self, user_input: String) -> AgentResult<ProcessResult> {
        // Add user message to conversation
        self.conversation_manager.add_user_message(user_input);

        let mut turns_taken = 0;
        let mut task_completed = false;
        let mut requires_user_input = false;
        let mut final_response = String::new();

        // Main conversation loop
        while turns_taken < self.max_turns && !task_completed && !requires_user_input {
            turns_taken += 1;

            // Build messages for API call
            let messages = self.conversation_manager.build_messages()?;

            // Call AI model
            let model_response = self.call_ai_model(&messages).await?;

            // Add assistant response to conversation
            self.conversation_manager
                .add_assistant_message(model_response.clone());

            // Check if response contains tool calls
            if self.tool_executor.contains_tool_call(&model_response) {
                // Execute tool call
                let execution_results =
                    self.tool_executor.execute_multiple_tools(&model_response)?;

                for execution_result in execution_results {
                    // Add tool result to conversation
                    self.conversation_manager.add_tool_result(
                        execution_result.tool_name.clone(),
                        execution_result.result.clone(),
                    );

                    // Check if task is completed or requires user input
                    if execution_result.task_completed {
                        task_completed = true;
                        final_response = self.extract_final_response(&execution_result)?;
                        break;
                    }

                    if execution_result.requires_user_input {
                        requires_user_input = true;
                        final_response = self.extract_user_input_request(&execution_result)?;
                        break;
                    }
                }
            } else {
                // No tool call found - this is an error according to requirements
                let error_message = "错误：AI模型必须调用工具，但未找到有效的工具调用";
                self.conversation_manager
                    .add_tool_result("error".to_string(), error_message.to_string());

                // Try one more time
                if turns_taken >= self.max_turns {
                    final_response = "抱歉，无法完成您的请求。请重新描述您的需求。".to_string();
                    break;
                }
            }
        }

        // Handle max turns reached
        if turns_taken >= self.max_turns && !task_completed && !requires_user_input {
            final_response =
                "抱歉，处理您的请求时达到了最大尝试次数。请重新描述您的需求。".to_string();
        }

        Ok(ProcessResult {
            response: final_response,
            task_completed,
            requires_user_input,
            turns_taken,
        })
    }

    /// Call the AI model API
    async fn call_ai_model(&self, messages: &[Message]) -> AgentResult<String> {
        let mut retries = 0;
        let max_retries = self.config.openai.max_retries;

        while retries <= max_retries {
            match self.call_ai_model_once(messages).await {
                Ok(response) => return Ok(response),
                Err(e) => {
                    retries += 1;
                    if retries > max_retries {
                        return Err(e);
                    }

                    // Wait before retry
                    tokio::time::sleep(Duration::from_millis(1000 * retries as u64)).await;
                }
            }
        }

        Err(AgentError::General("Max retries exceeded".to_string()))
    }

    /// Make a single API call to the AI model
    async fn call_ai_model_once(&self, messages: &[Message]) -> AgentResult<String> {
        let request_body = json!({
            "model": self.config.openai.model_id,
            "messages": messages,
            "temperature": 0.7,
            "max_tokens": 2000
        });

        let response = self
            .client
            .post(&format!("{}/chat/completions", self.config.openai.base_url))
            .header(
                "Authorization",
                format!("Bearer {}", self.config.openai.api_key),
            )
            .header("Content-Type", "application/json")
            .json(&request_body)
            .send()
            .await?;

        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_default();
            return Err(AgentError::General(format!(
                "HTTP request failed: {}",
                error_text
            )));
        }

        let response_json: Value = response.json().await?;

        let content = response_json
            .get("choices")
            .and_then(|choices| choices.get(0))
            .and_then(|choice| choice.get("message"))
            .and_then(|message| message.get("content"))
            .and_then(|content| content.as_str())
            .ok_or_else(|| AgentError::General("Invalid API response format".to_string()))?;

        Ok(content.to_string())
    }

    /// Extract final response from execution result
    fn extract_final_response(&self, execution_result: &ExecutionResult) -> AgentResult<String> {
        // Parse the JSON result to extract the actual answer
        if let Ok(result_json) = serde_json::from_str::<Value>(&execution_result.result) {
            if let Some(data) = result_json.get("data") {
                if let Some(answer) = data.get("answer") {
                    if let Some(answer_str) = answer.as_str() {
                        return Ok(answer_str.to_string());
                    }
                }

                if let Some(result) = data.get("result") {
                    if let Some(result_str) = result.as_str() {
                        return Ok(result_str.to_string());
                    }
                }
            }

            if let Some(message) = result_json.get("message") {
                if let Some(message_str) = message.as_str() {
                    return Ok(message_str.to_string());
                }
            }
        }

        // Fallback to the raw result
        Ok(execution_result.result.clone())
    }

    /// Extract user input request from execution result
    fn extract_user_input_request(
        &self,
        execution_result: &ExecutionResult,
    ) -> AgentResult<String> {
        // Parse the JSON result to extract the question
        if let Ok(result_json) = serde_json::from_str::<Value>(&execution_result.result) {
            if let Some(data) = result_json.get("data") {
                if let Some(question) = data.get("question") {
                    if let Some(question_str) = question.as_str() {
                        return Ok(question_str.to_string());
                    }
                }
            }

            if let Some(message) = result_json.get("message") {
                if let Some(message_str) = message.as_str() {
                    return Ok(message_str.to_string());
                }
            }
        }

        // Fallback to the raw result
        Ok(execution_result.result.clone())
    }

    /// Clear conversation history
    pub fn clear_conversation(&mut self) {
        self.conversation_manager.clear_history();
    }

    /// Get conversation history length
    pub fn conversation_length(&self) -> usize {
        self.conversation_manager.history_length()
    }
}
