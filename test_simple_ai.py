#!/usr/bin/env python3
"""
简单的AI Agent测试
"""

import subprocess
import os

def test_simple_ai():
    """简单测试AI Agent"""
    print("🤖 简单AI Agent测试...")
    
    # 设置API密钥
    api_key = "sk-b106452f497642b5a7921f26ecf8c2a6"
    env = os.environ.copy()
    env["OPENAI_API_KEY"] = api_key
    
    # 简单的测试命令
    test_input = "help me\nexit\n"
    
    try:
        # 启动brush shell
        process = subprocess.Popen(
            ["./target/debug/brush.exe"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            env=env
        )
        
        # 发送命令并等待结果
        stdout, stderr = process.communicate(input=test_input, timeout=20)
        
        print("📥 输出:")
        print(stdout)
        
        if stderr:
            print("\n📥 错误:")
            print(stderr)
        
        # 检查是否成功
        if "AiShell正在处理" in stdout:
            print("\n✅ AI Agent被成功触发！")
            return True
        else:
            print("\n❌ AI Agent未被触发")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    test_simple_ai()
