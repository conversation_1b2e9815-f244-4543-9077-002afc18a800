{"openai": {"base_url": "https://api.openai.com/v1", "model_id": "gpt-4", "api_key": "", "max_retries": 3, "timeout_seconds": 30}, "prompt": {"template_path": null, "template": "你是一个智能的命令行助手，能够帮助用户执行各种系统操作。\n\n当前环境信息：\n{ENVIRONMENT_DETAIL}\n\n你可以使用以下工具：\n{TOOL_DESCRIPTIONS}\n\n请根据用户的输入选择合适的工具来完成任务。每次只能调用一个工具。\n如果没有合适的工具，请使用ask_question工具询问更多信息，或使用final_answer工具给出最终回答。\n\n重要要求：\n1. 必须使用工具调用，不能直接回答\n2. 每次只能调用一个工具\n3. 工具调用必须使用XML格式\n4. 对于危险操作，要谨慎处理并询问确认", "tool_descriptions": {"ask_question": {"description": "当需要特定信息才能继续时使用（用户未提供足够细节时）", "example": "<ask_question>\n<question>您遇到问题的设备序列号是多少？</question>\n</ask_question>", "requirements": "尽早提问，获取必要信息"}, "final_answer": {"description": "当确认掌握足够信息时给出最终答复", "example": "<final_answer>\n<answer>错误代码EC205表示电池过热，建议暂停使用并检查散热孔</answer>\n<references>\n<reference>/docs/error_codes/EC205</reference>\n</references>\n</final_answer>", "requirements": "提供结构化解决方案和相关参考"}}}, "tools": {"enabled_tools": ["command_exec", "list_dir", "read_file", "search_files", "ask_question", "web_fetch", "update_todo_list", "final_answer", "write_file"], "tool_configs": {}}, "security": {"whitelist_path": null, "blacklist_path": null, "require_confirmation": true, "always_confirm": ["rm", "rmdir", "mv", "cp", "chmod", "chown"], "always_block": ["rm -rf /", "shutdown", "reboot", "halt"]}, "web_fetch": {"chrome_path": null, "timeout_seconds": 30, "max_content_length": 50000}}