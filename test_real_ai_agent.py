#!/usr/bin/env python3
"""
测试真实AI Agent调用的脚本
"""

import subprocess
import time
import sys
import os

def test_real_ai_agent():
    """测试真实的AI Agent调用"""
    print("🤖 测试真实AI Agent调用...")
    
    # 设置API密钥环境变量（从配置文件中读取）
    api_key = "sk-b106452f497642b5a7921f26ecf8c2a6"
    env = os.environ.copy()
    env["OPENAI_API_KEY"] = api_key
    
    # 创建测试命令
    test_commands = [
        "echo 'Starting real AI Agent test'",
        "help me list files",  # 这应该触发AI Agent
        "create a simple test file",  # 这也应该触发AI Agent
        "exit"
    ]
    
    try:
        print("📤 启动brush shell并发送命令...")
        
        # 启动brush shell
        process = subprocess.Popen(
            ["./target/debug/brush.exe"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            env=env  # 传递包含API密钥的环境变量
        )
        
        # 发送测试命令
        input_text = "\n".join(test_commands)
        stdout, stderr = process.communicate(input=input_text, timeout=30)
        
        print("📤 发送的命令:")
        for cmd in test_commands:
            print(f"  > {cmd}")
        
        print("\n📥 Shell输出:")
        print(stdout)
        
        if stderr:
            print("\n📥 错误输出:")
            print(stderr)
        
        # 分析输出
        success_indicators = [
            "AI Agent initialized successfully",
            "AI Agent processed input successfully", 
            "AiShell正在处理",
            "工具描述",
            "final_answer",
            "ask_question"
        ]
        
        found_indicators = []
        for indicator in success_indicators:
            if indicator in stdout or indicator in stderr:
                found_indicators.append(indicator)
        
        if found_indicators:
            print(f"\n✅ AI Agent成功调用！检测到指标: {found_indicators}")
            return True
        else:
            print("\n❌ 未检测到AI Agent成功调用的指标")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 测试超时")
        process.kill()
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_config_file():
    """测试配置文件是否正确"""
    print("\n📋 检查配置文件...")
    
    config_path = "agent_config.json"
    if os.path.exists(config_path):
        print(f"✅ 配置文件存在: {config_path}")
        
        try:
            import json
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            print(f"✅ API Base URL: {config['openai']['base_url']}")
            print(f"✅ Model ID: {config['openai']['model_id']}")
            print(f"✅ API Key: {config['openai']['api_key'][:10]}...")
            
            return True
        except Exception as e:
            print(f"❌ 配置文件解析失败: {e}")
            return False
    else:
        print(f"❌ 配置文件不存在: {config_path}")
        return False

def test_compilation():
    """测试编译"""
    print("\n🔨 测试编译...")
    
    result = subprocess.run(["cargo", "build"], 
                          capture_output=True, text=True, timeout=120)
    
    if result.returncode == 0:
        print("✅ 编译成功")
        return True
    else:
        print("❌ 编译失败")
        print(f"错误: {result.stderr}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始真实AI Agent测试")
    print("="*50)
    
    tests = [
        ("编译测试", test_compilation),
        ("配置文件检查", test_config_file),
        ("真实AI Agent调用测试", test_real_ai_agent),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*20} {test_name} {'='*20}")
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results[test_name] = False
    
    # 生成报告
    print("\n" + "="*60)
    print("📊 真实AI Agent测试报告")
    print("="*60)
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
    
    print("\n详细结果:")
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！AI Agent真实调用成功！")
        return 0
    else:
        print("\n⚠️ 部分测试失败，请检查相关功能。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
