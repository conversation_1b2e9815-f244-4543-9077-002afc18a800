//! Tests for the AI Agent system

#[cfg(test)]
mod tests {
    use super::*;
    use crate::agent::{Agent, AgentConfig};
    use crate::agent::config::{OpenAIConfig, PromptConfig, ToolConfig, SecurityConfig};
    use crate::agent::xml_parser::XmlParser;
    use crate::agent::tool_executor::ToolExecutor;
    use crate::agent::security::CommandValidator;
    use std::collections::HashMap;

    fn create_test_config() -> AgentConfig {
        AgentConfig {
            openai: OpenAIConfig {
                base_url: "https://api.openai.com/v1".to_string(),
                model_id: "gpt-4".to_string(),
                api_key: "test-key".to_string(),
                max_retries: 1,
                timeout_seconds: 10,
            },
            prompt: PromptConfig::default(),
            tools: ToolConfig::default(),
            security: SecurityConfig::default(),
            web_fetch: None,
        }
    }

    #[test]
    fn test_xml_parser_simple_tool_call() {
        let parser = XmlParser::new().unwrap();
        let xml = r#"<ask_question>
<question>What is your name?</question>
</ask_question>"#;
        
        let tool_call = parser.extract_tool_call(xml).unwrap();
        assert_eq!(tool_call.tool_name, "ask_question");
        assert_eq!(tool_call.parameters.get("question").unwrap(), "What is your name?");
    }

    #[test]
    fn test_xml_parser_complex_tool_call() {
        let parser = XmlParser::new().unwrap();
        let xml = r#"<command_exec>
<command>ls -la</command>
<confirmed>true</confirmed>
</command_exec>"#;
        
        let tool_call = parser.extract_tool_call(xml).unwrap();
        assert_eq!(tool_call.tool_name, "command_exec");
        assert_eq!(tool_call.parameters.get("command").unwrap(), "ls -la");
        assert_eq!(tool_call.parameters.get("confirmed").unwrap(), "true");
    }

    #[test]
    fn test_xml_parser_no_tool_call() {
        let parser = XmlParser::new().unwrap();
        let text = "This is just regular text without any tool calls.";
        
        let result = parser.extract_tool_call(text);
        assert!(result.is_err());
    }

    #[test]
    fn test_command_validator_safe_command() {
        let config = create_test_config();
        let validator = CommandValidator::new(&config).unwrap();
        
        let result = validator.validate_command("ls -la");
        assert!(result.allowed);
        assert!(!result.requires_confirmation);
    }

    #[test]
    fn test_command_validator_dangerous_command() {
        let config = create_test_config();
        let validator = CommandValidator::new(&config).unwrap();
        
        let result = validator.validate_command("rm important_file.txt");
        assert!(result.allowed);
        assert!(result.requires_confirmation);
    }

    #[test]
    fn test_command_validator_blocked_command() {
        let config = create_test_config();
        let validator = CommandValidator::new(&config).unwrap();
        
        let result = validator.validate_command("rm -rf /");
        assert!(!result.allowed);
    }

    #[test]
    fn test_tool_executor_creation() {
        let config = create_test_config();
        let executor = ToolExecutor::new(config).unwrap();
        
        let tools = executor.get_available_tools();
        assert!(!tools.is_empty());
        assert!(tools.contains(&"ask_question".to_string()));
        assert!(tools.contains(&"final_answer".to_string()));
    }

    #[test]
    fn test_tool_executor_ask_question() {
        let config = create_test_config();
        let executor = ToolExecutor::new(config).unwrap();
        
        let xml = r#"<ask_question>
<question>What is your favorite color?</question>
</ask_question>"#;
        
        let result = executor.execute_tool(xml).unwrap();
        assert_eq!(result.tool_name, "ask_question");
        assert!(result.requires_user_input);
        assert!(!result.task_completed);
    }

    #[test]
    fn test_tool_executor_final_answer() {
        let config = create_test_config();
        let executor = ToolExecutor::new(config).unwrap();
        
        let xml = r#"<final_answer>
<answer>The task has been completed successfully.</answer>
</final_answer>"#;
        
        let result = executor.execute_tool(xml).unwrap();
        assert_eq!(result.tool_name, "final_answer");
        assert!(!result.requires_user_input);
        assert!(result.task_completed);
    }

    #[test]
    fn test_tool_executor_list_dir() {
        let config = create_test_config();
        let executor = ToolExecutor::new(config).unwrap();
        
        let xml = r#"<list_dir>
<path>.</path>
</list_dir>"#;
        
        let result = executor.execute_tool(xml).unwrap();
        assert_eq!(result.tool_name, "list_dir");
        assert!(!result.requires_user_input);
        assert!(!result.task_completed);
    }

    #[test]
    fn test_tool_executor_unknown_tool() {
        let config = create_test_config();
        let executor = ToolExecutor::new(config).unwrap();
        
        let xml = r#"<unknown_tool>
<param>value</param>
</unknown_tool>"#;
        
        let result = executor.execute_tool(xml);
        assert!(result.is_err());
    }

    #[test]
    fn test_config_loading() {
        let config = AgentConfig::default();
        assert!(!config.openai.base_url.is_empty());
        assert!(!config.tools.enabled_tools.is_empty());
    }

    #[test]
    fn test_config_serialization() {
        let config = create_test_config();
        let json = serde_json::to_string(&config).unwrap();
        let deserialized: AgentConfig = serde_json::from_str(&json).unwrap();
        
        assert_eq!(config.openai.model_id, deserialized.openai.model_id);
        assert_eq!(config.openai.base_url, deserialized.openai.base_url);
    }

    #[tokio::test]
    async fn test_agent_creation() {
        let config = create_test_config();
        
        // This will fail without a real API key, but should test the creation logic
        let result = Agent::new(config);
        assert!(result.is_ok());
    }

    #[test]
    fn test_tool_result_json_serialization() {
        use crate::agent::tools::ToolResult;
        
        let result = ToolResult::success("Test message".to_string());
        let json = result.to_json();
        
        let parsed: serde_json::Value = serde_json::from_str(&json).unwrap();
        assert_eq!(parsed["status"], 200);
        assert_eq!(parsed["message"], "Test message");
    }

    #[test]
    fn test_tool_result_confirmation_required() {
        use crate::agent::tools::ToolResult;
        
        let result = ToolResult::confirmation_required(
            "Confirmation needed".to_string(),
            serde_json::json!({"command": "rm file.txt"})
        );
        
        assert!(result.requires_confirmation());
        assert!(!result.is_success());
    }

    #[test]
    fn test_environment_info_collection() {
        use crate::agent::conversation_manager::ConversationManager;
        
        let config = create_test_config();
        let manager = ConversationManager::new(config).unwrap();
        
        // Test that we can create a conversation manager
        assert_eq!(manager.history_length(), 0);
    }

    #[test]
    fn test_conversation_manager_message_handling() {
        use crate::agent::conversation_manager::ConversationManager;
        
        let config = create_test_config();
        let mut manager = ConversationManager::new(config).unwrap();
        
        manager.add_user_message("Hello".to_string());
        assert_eq!(manager.history_length(), 1);
        
        manager.add_assistant_message("Hi there!".to_string());
        assert_eq!(manager.history_length(), 2);
        
        manager.add_tool_result("test_tool".to_string(), "Tool result".to_string());
        assert_eq!(manager.history_length(), 3);
        
        manager.clear_history();
        assert_eq!(manager.history_length(), 0);
    }
}
