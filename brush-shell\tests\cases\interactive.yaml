name: "Interactive"
incompatible_configs: ["sh"]
cases:
  - name: "Basic interactive test"
    pty: true
    ignore_stdout: true
    stdin: |
      #expect-prompt
      echo hi
      #send:Enter
      #expect:hi
      #expect-prompt
      #send:Ctrl+D

  - name: "Simple tab completion"
    skip: true # TODO: Need to find the right way to enable this with a rich input backend.
    pty: true
    ignore_stdout: true
    test_files:
      - path: "test-file.txt"
        contents: |
          Hello, world.
    stdin: |
      #expect-prompt
      cat test-file.
      #send:Tab
      #send:Enter
      #expect:Hello, world.
      #expect-prompt
      #send:Ctrl+D
