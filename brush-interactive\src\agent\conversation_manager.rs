//! Conversation management for the AI Agent

use crate::agent::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Represents a message in the conversation
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Message {
    /// Role of the message sender
    pub role: String,
    /// Content of the message
    pub content: String,
    /// Optional tool name for tool messages
    #[serde(skip_serializing_if = "Option::is_none")]
    pub name: Option<String>,
}

/// Manages conversation history and system prompt construction
pub struct ConversationManager {
    /// Configuration for the agent
    #[allow(dead_code)]
    config: AgentConfig,
    /// Conversation history
    conversation_history: Vec<Message>,
    /// System prompt template
    system_prompt_template: String,
    /// Tool descriptions for the prompt
    tool_descriptions: HashMap<String, String>,
}

impl ConversationManager {
    /// Create a new conversation manager
    pub fn new(config: AgentConfig) -> AgentResult<Self> {
        let system_prompt_template = if let Some(ref template_path) = config.prompt.template_path {
            std::fs::read_to_string(template_path)
                .map_err(|e| AgentError::Config(format!("Failed to read template file: {}", e)))?
        } else if let Some(ref template) = config.prompt.template {
            template.clone()
        } else {
            return Err(AgentError::Config(
                "No system prompt template provided".to_string(),
            ));
        };

        let tool_descriptions = Self::build_tool_descriptions(&config);

        Ok(Self {
            config,
            conversation_history: Vec::new(),
            system_prompt_template,
            tool_descriptions,
        })
    }

    /// Add a user message to the conversation history
    pub fn add_user_message(&mut self, content: String) {
        let env_detail = self.collect_environment_info();
        let content_with_env = format!(
            "{}\n\n<environment_detail>\n{}\n</environment_detail>",
            content, env_detail
        );

        self.conversation_history.push(Message {
            role: "user".to_string(),
            content: content_with_env,
            name: None,
        });
    }

    /// Add an assistant message to the conversation history
    pub fn add_assistant_message(&mut self, content: String) {
        self.conversation_history.push(Message {
            role: "assistant".to_string(),
            content,
            name: None,
        });
    }

    /// Add a tool result to the conversation history
    pub fn add_tool_result(&mut self, tool_name: String, result: String) {
        self.conversation_history.push(Message {
            role: "tool".to_string(),
            content: result,
            name: Some(tool_name),
        });
    }

    /// Build messages for API call
    pub fn build_messages(&self) -> AgentResult<Vec<Message>> {
        let mut messages = Vec::new();

        // Add system message
        let system_prompt = self.build_system_prompt()?;
        messages.push(Message {
            role: "system".to_string(),
            content: system_prompt,
            name: None,
        });

        // Add conversation history
        messages.extend(self.conversation_history.clone());

        Ok(messages)
    }

    /// Build the system prompt with tool descriptions
    fn build_system_prompt(&self) -> AgentResult<String> {
        let tool_descriptions_text = self
            .tool_descriptions
            .values()
            .cloned()
            .collect::<Vec<_>>()
            .join("\n\n");

        let env_detail = self.collect_environment_info();

        let prompt = self
            .system_prompt_template
            .replace("{TOOL_DESCRIPTIONS}", &tool_descriptions_text)
            .replace("{ENVIRONMENT_DETAIL}", &env_detail);

        Ok(prompt)
    }

    /// Collect environment information
    fn collect_environment_info(&self) -> String {
        let mut env_info = Vec::new();

        // Operating system information
        env_info.push(format!(
            "操作系统: {} {}",
            std::env::consts::OS,
            std::env::consts::ARCH
        ));

        // Current time
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default();
        env_info.push(format!(
            "当前时间: {}",
            chrono::DateTime::from_timestamp(now.as_secs() as i64, 0)
                .unwrap_or_default()
                .format("%Y-%m-%d %H:%M:%S")
        ));

        // Shell type
        env_info.push("终端类型: brush shell".to_string());

        // Current directory
        if let Ok(current_dir) = std::env::current_dir() {
            env_info.push(format!("当前目录: {}", current_dir.display()));
        }

        // Directory listing (first 20 items)
        if let Ok(entries) = std::fs::read_dir(".") {
            let files: Vec<String> = entries
                .filter_map(|entry| entry.ok())
                .take(20)
                .map(|entry| entry.file_name().to_string_lossy().to_string())
                .collect();

            if !files.is_empty() {
                env_info.push(format!("目录内容: {}", files.join(", ")));
            }
        }

        env_info.join("\n")
    }

    /// Build tool descriptions from configuration
    fn build_tool_descriptions(config: &AgentConfig) -> HashMap<String, String> {
        let mut descriptions = HashMap::new();

        for tool_name in &config.tools.enabled_tools {
            if let Some(tool_desc) = config.prompt.tool_descriptions.get(tool_name) {
                let formatted_desc = format!(
                    "## {}\n工具描述：{}\n{}\n示例：\n{}\n要求：\n- {}",
                    tool_name,
                    tool_desc.description,
                    tool_desc.example,
                    tool_desc.example,
                    tool_desc.requirements
                );
                descriptions.insert(tool_name.clone(), formatted_desc);
            } else {
                // Provide default descriptions for common tools
                let default_desc = Self::get_default_tool_description(tool_name);
                descriptions.insert(tool_name.clone(), default_desc);
            }
        }

        descriptions
    }

    /// Get default tool description for common tools
    fn get_default_tool_description(tool_name: &str) -> String {
        match tool_name {
            "command_exec" => r#"## command_exec
工具描述：执行系统命令
<command_exec>
<command>[要执行的命令]</command>
<confirmed>[true/false，是否已确认]</confirmed>
</command_exec>
示例：
<command_exec>
<command>ls -la</command>
<confirmed>false</confirmed>
</command_exec>
要求：
- 危险命令需要用户确认"#
                .to_string(),
            "list_dir" => r#"## list_dir
工具描述：列出目录内容
<list_dir>
<path>[目录路径，默认为当前目录]</path>
</list_dir>
示例：
<list_dir>
<path>/home/<USER>/path>
</list_dir>
要求：
- 提供有效的目录路径"#
                .to_string(),
            "read_file" => r#"## read_file
工具描述：读取文件内容
<read_file>
<path>[文件路径]</path>
</read_file>
示例：
<read_file>
<path>/etc/hosts</path>
</read_file>
要求：
- 文件必须存在且可读"#
                .to_string(),
            "write_file" => r#"## write_file
工具描述：写入文件内容
<write_file>
<path>[文件路径]</path>
<content>[文件内容]</content>
</write_file>
示例：
<write_file>
<path>/tmp/test.txt</path>
<content>Hello World</content>
</write_file>
要求：
- 确保有写入权限"#
                .to_string(),
            "search_files" => r#"## search_files
工具描述：在文件中搜索内容
<search_files>
<pattern>[搜索模式]</pattern>
<path>[搜索路径，默认为当前目录]</path>
</search_files>
示例：
<search_files>
<pattern>TODO</pattern>
<path>./src</path>
</search_files>
要求：
- 提供有效的搜索模式"#
                .to_string(),
            "web_fetch" => r#"## web_fetch
工具描述：获取网页内容
<web_fetch>
<url>[网页URL]</url>
</web_fetch>
示例：
<web_fetch>
<url>https://example.com</url>
</web_fetch>
要求：
- 提供有效的URL"#
                .to_string(),
            _ => format!(
                "## {}\n工具描述：{} 工具\n要求：\n- 按照工具规范使用",
                tool_name, tool_name
            ),
        }
    }

    /// Clear conversation history
    pub fn clear_history(&mut self) {
        self.conversation_history.clear();
    }

    /// Get conversation history length
    pub fn history_length(&self) -> usize {
        self.conversation_history.len()
    }
}
