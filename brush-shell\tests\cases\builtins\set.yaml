name: "Builtins: set"
cases:
  - name: "set with no args"
    stdin: |
      MYVARIABLE=VALUE
      set > set-output.txt
      grep MYVARIABLE set-output.txt

      # Remove set-output.txt to avoid it being byte-for-byte compared.
      rm set-output.txt

  - name: "Basic set usage"
    stdin: |
      set a b c d
      echo ${*}

  - name: "set with options"
    stdin: |
      function dumpopts {
          # Dump the options
          echo "[Options: $1]"
          echo "set options: " $-
          shopt -p -o pipefail
      }

      set -e -u -o pipefail
      dumpopts enabled
      echo '*: ' $*

      set +e +u +o pipefail
      dumpopts disabled
      echo '*: ' $*

  - name: "set with multiple combined options"
    stdin: |
      function dumpopts {
          # Dump the options
          echo "[Options: $1]"
          echo "set options: " $-
          shopt -p -o pipefail
      }

      set -euo pipefail
      dumpopts enabled
      echo '$*: ' $*

      set +euo pipefail
      dumpopts disabled
      echo '$*: ' $*

  - name: "set clearing args"
    stdin: |
      set a b c
      echo ${*}
      set a
      echo ${*}

  - name: "set with -"
    stdin: |
      set - a b c
      echo "args: " ${*}
      set -
      echo "args: " ${*}

  - name: "set with --"
    stdin: |
      set -- a b c
      echo "args: " ${*}
      set --
      echo "args: " ${*}

  - name: "set with option-looking args"
    stdin: |
      set -- a -v
      echo ${*}

      set - a -v
      echo ${*}

      set a -v
      echo ${*}

      set -- a +x
      echo ${*}

      set - a +x
      echo ${*}

      set a +x
      echo ${*}

  - name: "function-local set"
    known_failure: true
    stdin: |
      function f() {
          local -
          set -x
          echo "In func"
      }

      echo "Before func"
      f
      echo "After func"
