name: "Compound commands: while"
cases:
  - name: "Single-line while loop"
    stdin: |
      while false; do echo 'In loop'; done

  - name: "break in while loop"
    stdin: |
      while true; do
        echo 'In loop'
        break
      done

  - name: "break 2 in nested loops"
    stdin: |
      while false; do
        echo 'Starting inner loop'
        while true; do
          echo 'In loop'
          break 2
        done
        echo 'Finished inner loop'
      done

  - name: "Arithmetic in while loop"
    stdin: |
      i=5
      while ((i > 0)); do echo $i; i=$((i - 1)); done

  - name: "Alternative arithmetic in while loop"
    stdin: |
      c=0
      limit=4
      while [ $c -lt $limit ]; do
        case "$c" in
        0)
          echo "0"
          ;;
        1)
          echo "1"
          ;;
        *)
          break
          ;;
        esac
        ((c++))
      done
      echo "Done"
