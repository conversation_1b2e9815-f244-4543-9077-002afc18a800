name: "Builtins: read"
cases:
  - name: "Basic read usage from file"
    test_files:
      - path: "data.txt"
        contents: |
          a 1
          b 2
    stdin: |
      while read name num; do echo "Hello, $name => $num"; done < data.txt

  - name: "Basic read usage from pipe"
    stdin: |
      echo "1."
      (echo a; echo b) | while read name; do echo "Hello, $name"; done

      echo "2."
      (echo "a b") | while read name; do echo "Hello, 1:$name REPLY:$REPLY"; done

      echo "3."
      (echo "a b") | while read -a arr; do declare -p arr; done

  - name: "read from here string"
    stdin: |
      read myvar <<< "hello"
      echo "myvar: ${myvar}"

  - name: "read from process substitution"
    stdin: |
      read myvar < <(echo hello)
      echo "myvar: ${myvar}"

  - name: "read with custom IFS"
    stdin: |
      content="    a    b    c "
      while IFS= read line; do
          echo "LINE: '$line'"
      done <<<"${content}"

  - name: "read text with tabs and custom IFS"
    stdin: |
      while IFS="" read myvar; do
          echo "myvar1: |${myvar}|"
      done < <(printf "a\tb\nc d\te\n")

  - name: "read with empty entries"
    stdin: |
      (echo "x"; echo ""; echo "y"; echo ""; echo "") | while read line; do echo "LINE: '$line'"; done
      echo 'x,,y,z' | (IFS=',' read READ1 READ2 READ3 READ4; declare -p READ1; declare -p READ2; declare -p READ3; declare -p READ4)

  - name: "read -a with empty lines"
    stdin: |
      (echo "hi"; echo ""; echo "there"; echo ""; echo "you") | (read -a READ; declare -p READ)
      (echo -e "hi\t\tthere\t\tyou") | (read -a READ -d $'\t'; declare -p READ)

  - name: "read -a with empty interior field"
    stdin: |
      echo -e -n "hi||there||you" | (IFS='|' read -a READ; declare -p READ)
      echo -e -n "hi  there  you " | (IFS=' ' read -a READ; declare -p READ)
      echo -e -n "hi  there  you " | (read -a READ; declare -p READ)

  - name: "read -a with empty leading field"
    stdin: |
      echo -e -n "|hi|there|you" | (IFS='|' read -a READ; declare -p READ)

  - name: "read -a with empty trailing field"
    known_failure: true # Needs further investigation
    stdin: |
      echo -e -n "hi|there|you|" | (IFS='|' read -a READ; declare -p READ)

  - name: "read -a with empty entries + empty delimiter"
    stdin: |
      (echo "hi"; echo ""; echo "there"; echo ""; echo "you") | (read -a READ -d ''; declare -p READ)
      echo -e -n "hi\n\nthere\n\nyou\n" | (read -a READ -d ''; declare -p READ)

  - name: "read -a with empty entries + empty delimiter + custom IFS"
    stdin: |
      (echo 'x,,y,z'; echo 'w,v'; echo ''; echo ''; echo 'm') | (IFS=',' read -d "" -a READ; declare -p READ)

  - name: "read with empty delimiter"
    min_oracle_version: 5.2 # \n renders differently in older shell versions
    stdin: |
      echo x | (read -d ""; declare -p REPLY)
