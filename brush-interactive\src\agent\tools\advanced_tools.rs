//! Advanced tools for AI Agent interactions

use super::{Tool, ToolResult};
use crate::agent::{<PERSON><PERSON><PERSON><PERSON>, AgentR<PERSON>ult};
use serde_json::Value;
use std::collections::HashMap;

/// Tool for asking questions to the user
pub struct AskQuestionTool;

impl Tool for AskQuestionTool {
    fn name(&self) -> &str {
        "ask_question"
    }

    fn description(&self) -> &str {
        "Ask a question to the user when more information is needed"
    }

    fn execute(&self, parameters: HashMap<String, String>) -> AgentResult<ToolResult> {
        let question = parameters
            .get("question")
            .ok_or_else(|| AgentError::ToolExecution("Missing 'question' parameter".to_string()))?;

        // This tool indicates that the agent needs more information
        // The question will be displayed to the user
        Ok(ToolResult::success_with_data(
            format!("需要更多信息: {}", question),
            serde_json::json!({
                "type": "question",
                "question": question,
                "requires_user_input": true
            }),
        ))
    }
}

/// Tool for providing final answers
pub struct FinalAnswerTool;

impl Tool for FinalAnswerTool {
    fn name(&self) -> &str {
        "final_answer"
    }

    fn description(&self) -> &str {
        "Provide a final answer when the task is complete"
    }

    fn execute(&self, parameters: HashMap<String, String>) -> AgentResult<ToolResult> {
        let answer = parameters
            .get("answer")
            .ok_or_else(|| AgentError::ToolExecution("Missing 'answer' parameter".to_string()))?;

        let references = parameters.get("references").map_or("", |v| v);

        let mut result_data = serde_json::json!({
            "type": "final_answer",
            "answer": answer,
            "task_completed": true
        });

        if !references.is_empty() {
            result_data["references"] = Value::String(references.to_string());
        }

        Ok(ToolResult::success_with_data(
            format!("任务完成: {}", answer),
            result_data,
        ))
    }
}

/// Tool for updating todo lists
pub struct UpdateTodoListTool;

impl Tool for UpdateTodoListTool {
    fn name(&self) -> &str {
        "update_todo_list"
    }

    fn description(&self) -> &str {
        "Update or manage todo lists"
    }

    fn execute(&self, parameters: HashMap<String, String>) -> AgentResult<ToolResult> {
        let action = parameters
            .get("action")
            .ok_or_else(|| AgentError::ToolExecution("Missing 'action' parameter".to_string()))?;

        let item = parameters.get("item").map_or("", |v| v);
        let list_name = parameters.get("list_name").map_or("default", |v| v);

        match action.as_str() {
            "add" => {
                if item.is_empty() {
                    return Ok(ToolResult::error(
                        400,
                        "添加项目时需要提供 'item' 参数".to_string(),
                    ));
                }

                // In a real implementation, this would save to a file or database
                Ok(ToolResult::success(format!(
                    "已将 '{}' 添加到 {} 待办列表",
                    item, list_name
                )))
            }
            "remove" => {
                if item.is_empty() {
                    return Ok(ToolResult::error(
                        400,
                        "删除项目时需要提供 'item' 参数".to_string(),
                    ));
                }

                Ok(ToolResult::success(format!(
                    "已从 {} 待办列表中删除 '{}'",
                    list_name, item
                )))
            }
            "list" => {
                // In a real implementation, this would read from a file or database
                Ok(ToolResult::success(format!(
                    "{} 待办列表:\n- 示例项目1\n- 示例项目2",
                    list_name
                )))
            }
            "complete" => {
                if item.is_empty() {
                    return Ok(ToolResult::error(
                        400,
                        "完成项目时需要提供 'item' 参数".to_string(),
                    ));
                }

                Ok(ToolResult::success(format!("已标记 '{}' 为完成状态", item)))
            }
            _ => Ok(ToolResult::error(
                400,
                format!(
                    "不支持的操作: {}。支持的操作: add, remove, list, complete",
                    action
                ),
            )),
        }
    }
}

/// Tool for attempt completion (indicates task is done)
pub struct AttemptCompletionTool;

impl Tool for AttemptCompletionTool {
    fn name(&self) -> &str {
        "attempt_completion"
    }

    fn description(&self) -> &str {
        "Attempt to complete the current task"
    }

    fn execute(&self, parameters: HashMap<String, String>) -> AgentResult<ToolResult> {
        let result = parameters
            .get("result")
            .ok_or_else(|| AgentError::ToolExecution("Missing 'result' parameter".to_string()))?;

        let command = parameters.get("command").map_or("", |v| v);

        let mut result_data = serde_json::json!({
            "type": "attempt_completion",
            "result": result,
            "task_completed": true
        });

        if !command.is_empty() {
            result_data["command"] = Value::String(command.to_string());
        }

        Ok(ToolResult::success_with_data(
            format!("任务尝试完成: {}", result),
            result_data,
        ))
    }
}

/// Tool for general text processing
pub struct TextProcessTool;

impl Tool for TextProcessTool {
    fn name(&self) -> &str {
        "text_process"
    }

    fn description(&self) -> &str {
        "Process and analyze text content"
    }

    fn execute(&self, parameters: HashMap<String, String>) -> AgentResult<ToolResult> {
        let text = parameters
            .get("text")
            .ok_or_else(|| AgentError::ToolExecution("Missing 'text' parameter".to_string()))?;

        let operation = parameters.get("operation").map_or("analyze", |v| v);

        match operation {
            "analyze" => {
                let word_count = text.split_whitespace().count();
                let char_count = text.chars().count();
                let line_count = text.lines().count();

                let analysis = format!(
                    "文本分析结果:\n- 字符数: {}\n- 单词数: {}\n- 行数: {}",
                    char_count, word_count, line_count
                );

                Ok(ToolResult::success_with_data(
                    analysis,
                    serde_json::json!({
                        "word_count": word_count,
                        "char_count": char_count,
                        "line_count": line_count
                    }),
                ))
            }
            "uppercase" => Ok(ToolResult::success(text.to_uppercase())),
            "lowercase" => Ok(ToolResult::success(text.to_lowercase())),
            "reverse" => {
                let reversed: String = text.chars().rev().collect();
                Ok(ToolResult::success(reversed))
            }
            _ => Ok(ToolResult::error(
                400,
                format!("不支持的文本操作: {}", operation),
            )),
        }
    }
}
