use crate::error;

pub(crate) fn get_self_user_and_system_time()
-> Result<(std::time::Duration, std::time::Duration), error::Error> {
    let usage = nix::sys::resource::getrusage(nix::sys::resource::UsageWho::RUSAGE_SELF)?;
    Ok((
        convert_rusage_time(usage.user_time()),
        convert_rusage_time(usage.system_time()),
    ))
}

pub(crate) fn get_children_user_and_system_time()
-> Result<(std::time::Duration, std::time::Duration), error::Error> {
    let usage = nix::sys::resource::getrusage(nix::sys::resource::UsageWho::RUSAGE_CHILDREN)?;
    Ok((
        convert_rusage_time(usage.user_time()),
        convert_rusage_time(usage.system_time()),
    ))
}

const fn convert_rusage_time(time: nix::sys::time::TimeVal) -> std::time::Duration {
    #[expect(clippy::cast_sign_loss)]
    #[expect(clippy::cast_possible_truncation)]
    std::time::Duration::new(time.tv_sec() as u64, time.tv_usec() as u32 * 1000)
}
