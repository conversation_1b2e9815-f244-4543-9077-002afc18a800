//! Basic tools for file system operations and command execution

use super::{<PERSON><PERSON>, ToolResult};
use crate::agent::security::CommandValidator;
use crate::agent::{AgentError, AgentResult};
use std::collections::HashMap;
use std::path::Path;
use std::process::Command;

/// Tool for executing system commands
pub struct CommandExecTool {
    validator: CommandValidator,
}

impl CommandExecTool {
    /// Create a new command execution tool
    pub fn new(validator: CommandValidator) -> Self {
        Self { validator }
    }
}

impl Tool for CommandExecTool {
    fn name(&self) -> &str {
        "command_exec"
    }

    fn description(&self) -> &str {
        "Execute system commands with security validation"
    }

    fn execute(&self, parameters: HashMap<String, String>) -> AgentResult<ToolResult> {
        let command = parameters
            .get("command")
            .ok_or_else(|| AgentError::ToolExecution("Missing 'command' parameter".to_string()))?;

        let confirmed = parameters
            .get("confirmed")
            .map(|s| s.to_lowercase() == "true")
            .unwrap_or(false);

        // Validate command
        let validation = self.validator.validate_command(command);

        if !validation.allowed {
            return Ok(ToolResult::error(
                403,
                format!("命令执行被拒绝: {}", validation.reason),
            ));
        }

        if validation.requires_confirmation && !confirmed {
            return Ok(ToolResult::confirmation_required(
                format!("命令需要确认才能执行: {}", command),
                serde_json::json!({
                    "command": command,
                    "requiresConfirmation": true
                }),
            ));
        }

        // Execute command
        self.execute_command_internal(command)
    }
}

impl CommandExecTool {
    fn execute_command_internal(&self, command: &str) -> AgentResult<ToolResult> {
        let output = if cfg!(target_os = "windows") {
            Command::new("cmd").args(["/C", command]).output()
        } else {
            Command::new("sh").args(["-c", command]).output()
        };

        match output {
            Ok(output) => {
                let stdout = String::from_utf8_lossy(&output.stdout);
                let stderr = String::from_utf8_lossy(&output.stderr);

                let result_text = if !stdout.is_empty() {
                    stdout.to_string()
                } else if !stderr.is_empty() {
                    stderr.to_string()
                } else {
                    "命令执行完成，无输出".to_string()
                };

                if output.status.success() {
                    Ok(ToolResult::success(result_text))
                } else {
                    Ok(ToolResult::error(
                        500,
                        format!("命令执行失败: {}", result_text),
                    ))
                }
            }
            Err(e) => Ok(ToolResult::error(500, format!("命令执行错误: {}", e))),
        }
    }
}

/// Tool for listing directory contents
pub struct ListDirTool;

impl Tool for ListDirTool {
    fn name(&self) -> &str {
        "list_dir"
    }

    fn description(&self) -> &str {
        "List directory contents"
    }

    fn execute(&self, parameters: HashMap<String, String>) -> AgentResult<ToolResult> {
        let path = parameters.get("path").map(|s| s.as_str()).unwrap_or(".");

        match std::fs::read_dir(path) {
            Ok(entries) => {
                let mut files = Vec::new();
                for entry in entries {
                    if let Ok(entry) = entry {
                        files.push(entry.file_name().to_string_lossy().to_string());
                    }
                }

                let result = format!("目录 {} 的内容:\n{}", path, files.join("\n"));
                Ok(ToolResult::success(result))
            }
            Err(e) => Ok(ToolResult::error(500, format!("列出目录失败: {}", e))),
        }
    }
}

/// Tool for reading file contents
pub struct ReadFileTool;

impl Tool for ReadFileTool {
    fn name(&self) -> &str {
        "read_file"
    }

    fn description(&self) -> &str {
        "Read file contents"
    }

    fn execute(&self, parameters: HashMap<String, String>) -> AgentResult<ToolResult> {
        let path = parameters
            .get("path")
            .ok_or_else(|| AgentError::ToolExecution("Missing 'path' parameter".to_string()))?;

        match std::fs::read_to_string(path) {
            Ok(content) => {
                let result = if content.len() > 5000 {
                    format!("文件内容 (前5000字符):\n{}", &content[..5000])
                } else {
                    format!("文件内容:\n{}", content)
                };
                Ok(ToolResult::success(result))
            }
            Err(e) => Ok(ToolResult::error(500, format!("读取文件失败: {}", e))),
        }
    }
}

/// Tool for writing file contents
pub struct WriteFileTool;

impl Tool for WriteFileTool {
    fn name(&self) -> &str {
        "write_file"
    }

    fn description(&self) -> &str {
        "Write content to a file"
    }

    fn execute(&self, parameters: HashMap<String, String>) -> AgentResult<ToolResult> {
        let path = parameters
            .get("path")
            .ok_or_else(|| AgentError::ToolExecution("Missing 'path' parameter".to_string()))?;
        let content = parameters
            .get("content")
            .ok_or_else(|| AgentError::ToolExecution("Missing 'content' parameter".to_string()))?;

        // Ensure parent directory exists
        if let Some(parent) = Path::new(path).parent() {
            if let Err(e) = std::fs::create_dir_all(parent) {
                return Ok(ToolResult::error(500, format!("创建目录失败: {}", e)));
            }
        }

        match std::fs::write(path, content) {
            Ok(_) => Ok(ToolResult::success(format!("文件 {} 已成功写入", path))),
            Err(e) => Ok(ToolResult::error(500, format!("写入文件失败: {}", e))),
        }
    }
}

/// Tool for searching files
pub struct SearchFilesTool;

impl Tool for SearchFilesTool {
    fn name(&self) -> &str {
        "search_files"
    }

    fn description(&self) -> &str {
        "Search for patterns in files"
    }

    fn execute(&self, parameters: HashMap<String, String>) -> AgentResult<ToolResult> {
        let pattern = parameters
            .get("pattern")
            .ok_or_else(|| AgentError::ToolExecution("Missing 'pattern' parameter".to_string()))?;
        let search_path = parameters.get("path").map(|s| s.as_str()).unwrap_or(".");

        let mut results = Vec::new();

        if let Ok(entries) = std::fs::read_dir(search_path) {
            for entry in entries.flatten() {
                if let Ok(file_type) = entry.file_type() {
                    if file_type.is_file() {
                        if let Ok(content) = std::fs::read_to_string(entry.path()) {
                            for (line_num, line) in content.lines().enumerate() {
                                if line.contains(pattern) {
                                    results.push(format!(
                                        "{}:{}: {}",
                                        entry.path().display(),
                                        line_num + 1,
                                        line.trim()
                                    ));
                                }
                            }
                        }
                    }
                }
            }
        }

        if results.is_empty() {
            Ok(ToolResult::success(format!(
                "在 {} 中未找到包含 '{}' 的内容",
                search_path, pattern
            )))
        } else {
            let result = format!("搜索结果 (模式: '{}'):\n{}", pattern, results.join("\n"));
            Ok(ToolResult::success(result))
        }
    }
}
