//! Web-related tools for the AI Agent

use super::{<PERSON><PERSON>, <PERSON>l<PERSON><PERSON><PERSON>};
use crate::agent::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, AgentR<PERSON>ult};
use std::collections::HashMap;
use std::time::Duration;

/// Tool for fetching web content
pub struct WebFetchTool {
    config: Option<crate::agent::config::WebFetchConfig>,
    client: reqwest::Client,
}

impl WebFetchTool {
    /// Create a new web fetch tool
    pub fn new(config: &AgentConfig) -> Self {
        let client = reqwest::Client::builder()
            .timeout(Duration::from_secs(
                config
                    .web_fetch
                    .as_ref()
                    .map(|c| c.timeout_seconds)
                    .unwrap_or(30),
            ))
            .user_agent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
            .build()
            .unwrap_or_default();

        Self {
            config: config.web_fetch.clone(),
            client,
        }
    }
}

impl Tool for WebFetchTool {
    fn name(&self) -> &str {
        "web_fetch"
    }

    fn description(&self) -> &str {
        "Fetch content from web pages with JavaScript rendering support"
    }

    fn execute(&self, parameters: HashMap<String, String>) -> AgentResult<ToolResult> {
        let url = parameters
            .get("url")
            .ok_or_else(|| AgentError::ToolExecution("Missing 'url' parameter".to_string()))?;

        // Validate URL
        if !url.starts_with("http://") && !url.starts_with("https://") {
            return Ok(ToolResult::error(
                400,
                "URL必须以http://或https://开头".to_string(),
            ));
        }

        // Try simple HTTP fetch first
        match self.fetch_simple(url) {
            Ok(content) => Ok(content),
            Err(_) => {
                // If simple fetch fails, try with headless browser (if available)
                #[cfg(feature = "headless_chrome")]
                {
                    self.fetch_with_browser(url)
                }
                #[cfg(not(feature = "headless_chrome"))]
                {
                    Ok(ToolResult::error(
                        500,
                        "网页获取失败，且未启用浏览器支持".to_string(),
                    ))
                }
            }
        }
    }
}

impl WebFetchTool {
    fn fetch_simple(&self, url: &str) -> AgentResult<ToolResult> {
        let rt = tokio::runtime::Runtime::new()?;

        rt.block_on(async {
            let response = self.client.get(url).send().await?;

            if !response.status().is_success() {
                return Ok(ToolResult::error(
                    response.status().as_u16(),
                    format!("HTTP请求失败: {}", response.status()),
                ));
            }

            let content = response.text().await?;
            let max_length = self
                .config
                .as_ref()
                .map(|c| c.max_content_length)
                .unwrap_or(50000);

            let truncated_content = if content.len() > max_length {
                format!(
                    "{}...\n\n[内容已截断，原长度: {} 字符]",
                    &content[..max_length],
                    content.len()
                )
            } else {
                content
            };

            Ok(ToolResult::success_with_data(
                format!("成功获取网页内容: {}", url),
                serde_json::json!({
                    "url": url,
                    "content": truncated_content,
                    "content_length": truncated_content.len()
                }),
            ))
        })
    }

    #[cfg(feature = "headless_chrome")]
    fn fetch_with_browser(&self, url: &str) -> AgentResult<ToolResult> {
        use headless_chrome::{Browser, LaunchOptions};

        let launch_options = LaunchOptions::default_builder()
            .headless(true)
            .sandbox(false)
            .build()
            .map_err(|e| AgentError::General(format!("Failed to create launch options: {}", e)))?;

        let browser = Browser::new(launch_options)
            .map_err(|e| AgentError::General(format!("Failed to launch browser: {}", e)))?;

        let tab = browser
            .new_tab()
            .map_err(|e| AgentError::General(format!("Failed to create tab: {}", e)))?;

        tab.navigate_to(url)
            .map_err(|e| AgentError::General(format!("Failed to navigate: {}", e)))?;

        tab.wait_until_navigated()
            .map_err(|e| AgentError::General(format!("Failed to wait for navigation: {}", e)))?;

        // Remove scripts and styles
        let _ = tab.evaluate(
            r#"
            document.querySelectorAll('script, style').forEach(el => el.remove());
            "#,
            false,
        );

        // Get main content
        let content = tab
            .evaluate(
                r#"
            const mainElement = document.querySelector('main, article, .content, #content, body');
            mainElement ? mainElement.innerText : document.body.innerText;
            "#,
                false,
            )
            .map_err(|e| AgentError::General(format!("Failed to extract content: {}", e)))?;

        let content_str = content
            .value
            .and_then(|v| v.as_str())
            .unwrap_or("")
            .to_string();

        let max_length = self
            .config
            .as_ref()
            .map(|c| c.max_content_length)
            .unwrap_or(50000);

        let truncated_content = if content_str.len() > max_length {
            format!(
                "{}...\n\n[内容已截断，原长度: {} 字符]",
                &content_str[..max_length],
                content_str.len()
            )
        } else {
            content_str
        };

        Ok(ToolResult::success_with_data(
            format!("成功获取网页内容 (使用浏览器): {}", url),
            serde_json::json!({
                "url": url,
                "content": truncated_content,
                "content_length": truncated_content.len(),
                "method": "browser"
            }),
        ))
    }
}

/// Tool for making HTTP requests
pub struct HttpRequestTool {
    client: reqwest::Client,
}

impl HttpRequestTool {
    /// Create a new HTTP request tool
    pub fn new() -> Self {
        let client = reqwest::Client::builder()
            .timeout(Duration::from_secs(30))
            .build()
            .unwrap_or_default();

        Self { client }
    }
}

impl Tool for HttpRequestTool {
    fn name(&self) -> &str {
        "http_request"
    }

    fn description(&self) -> &str {
        "Make HTTP requests to APIs"
    }

    fn execute(&self, parameters: HashMap<String, String>) -> AgentResult<ToolResult> {
        let url = parameters
            .get("url")
            .ok_or_else(|| AgentError::ToolExecution("Missing 'url' parameter".to_string()))?;

        let method = parameters.get("method").map_or("GET", |v| v).to_uppercase();
        let body = parameters.get("body");
        let headers_str = parameters.get("headers");

        let rt = tokio::runtime::Runtime::new()?;

        rt.block_on(async {
            let mut request = match method.as_str() {
                "GET" => self.client.get(url),
                "POST" => self.client.post(url),
                "PUT" => self.client.put(url),
                "DELETE" => self.client.delete(url),
                _ => {
                    return Ok(ToolResult::error(
                        400,
                        format!("不支持的HTTP方法: {}", method),
                    ));
                }
            };

            // Add headers if provided
            if let Some(headers_str) = headers_str {
                if let Ok(headers_json) =
                    serde_json::from_str::<HashMap<String, String>>(headers_str)
                {
                    for (key, value) in headers_json {
                        request = request.header(&key, &value);
                    }
                }
            }

            // Add body if provided
            if let Some(body) = body {
                request = request.body(body.clone());
            }

            match request.send().await {
                Ok(response) => {
                    let status = response.status();
                    let headers: HashMap<String, String> = response
                        .headers()
                        .iter()
                        .map(|(k, v)| (k.to_string(), v.to_str().unwrap_or("").to_string()))
                        .collect();

                    match response.text().await {
                        Ok(body) => Ok(ToolResult::success_with_data(
                            format!("HTTP请求成功: {} {}", method, status),
                            serde_json::json!({
                                "status": status.as_u16(),
                                "headers": headers,
                                "body": body
                            }),
                        )),
                        Err(e) => Ok(ToolResult::error(500, format!("读取响应失败: {}", e))),
                    }
                }
                Err(e) => Ok(ToolResult::error(500, format!("HTTP请求失败: {}", e))),
            }
        })
    }
}

impl Default for HttpRequestTool {
    fn default() -> Self {
        Self::new()
    }
}
