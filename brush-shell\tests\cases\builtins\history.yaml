name: "Builtins: history"
cases:
  - name: "basic history saving"
    args: ["-o", "history"]
    env:
      HISTFILE: "history-file.txt"
    stdin: |
      echo something
      echo else

  - name: "existing history file"
    pty: true
    env:
      HISTFILE: "history-file.txt"
    test_files:
      - path: "history-file.txt"
        contents: |
          a b c
          1 2 3
    ignore_stdout: true # Prompts and other don't quite line up
    stdin: |
      #expect-prompt
      history >history-output.txt
      #send:Enter
      #expect-prompt
      #send:Ctrl+D

  - name: "existing history file with timestamps"
    pty: true
    env:
      HISTFILE: "history-file.txt"
    test_files:
      - path: "history-file.txt"
        contents: |
          #1750000000
          a b c
          #1750000001
          1 2 3
    ignore_stdout: true # Prompts and other don't quite line up
    stdin: |
      #expect-prompt
      history >history-output.txt
      #send:Enter
      #expect-prompt
      #send:Ctrl+D

  - name: "existing history file with timestamps and HISTTIMEFORMAT"
    pty: true
    env:
      HISTFILE: "history-file.txt"
      HISTTIMEFORMAT: "%C "
    test_files:
      - path: "history-file.txt"
        contents: |
          #1750000000
          a b c
          #1750000001
          1 2 3
    ignore_stdout: true # Prompts and other don't quite line up
    stdin: |
      #expect-prompt
      #send:Ctrl+D

  - name: "history N"
    args: ["-o", "history"]
    env:
      HISTFILE: "history-file.txt"
    stdin: |
      : cmd1
      : cmd2
      history 2

  - name: "history with duplicates"
    args: ["-o", "history"]
    env:
      HISTFILE: "history-file.txt"
    stdin: |
      : cmd1
      : cmd1
      : cmd1
      : cmd2
      : cmd1
      history

  - name: "history -c"
    args: ["-o", "history"]
    env:
      HISTFILE: "history-file.txt"
    stdin: |
      : cmd1
      history -c
      history

  - name: "history -d"
    args: ["-o", "history"]
    env:
      HISTFILE: "history-file.txt"
    stdin: |
      : cmd1
      history -d 1
      history

  - name: "history -d: negative offset"
    args: ["-o", "history"]
    env:
      HISTFILE: "history-file.txt"
    stdin: |
      : cmd1
      : cmd2
      history -d -2
      history

  - name: "history -a"
    args: ["-o", "history"]
    env:
      HISTFILE: "history-file.txt"
    stdin: |
      echo "[histfile]"
      cat $HISTFILE

      history -a

      echo "[histfile after -a]"
      cat $HISTFILE

      history -a

      echo "[histfile after second -a]"
      cat $HISTFILE

  - name: "history -w"
    args: ["-o", "history"]
    env:
      HISTFILE: "history-file.txt"
    stdin: |
      echo "[histfile]"
      cat $HISTFILE

      history -w

      echo "[histfile after -w]"
      cat $HISTFILE

      history -w

      echo "[histfile after second -w]"
      cat $HISTFILE

  - name: "history -w with explicit file"
    args: ["-o", "history"]
    env:
      HISTFILE: "history-file.txt"
    stdin: |
      : cmd1
      history -w some-file.txt

  - name: "history -s"
    known_failure: true # NOTE: bash excludes the history command itself from history
    args: ["-o", "history"]
    env:
      HISTFILE: "history-file.txt"
    stdin: |
      history -s a b c
      history

  - name: "HISTCMD"
    args: ["-o", "history"]
    env:
      HISTFILE: "history-file.txt"
    stdin: |
      : cmd1
      echo $HISTCMD
      : cmd2
      echo $HISTCMD
      : cmd3
      echo $HISTCMD

  - name: "HISTTIMEFORMAT"
    known_failure: true # TODO: needs triage
    args: ["-o", "history"]
    env:
      HISTFILE: "history-file.txt"
      HISTTIMEFORMAT: "century=%C "
    stdin: |
      : cmd1
      : cmd2
      history
