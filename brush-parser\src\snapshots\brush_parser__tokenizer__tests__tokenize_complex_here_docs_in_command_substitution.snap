---
source: brush-parser/src/tokenizer.rs
expression: "test_tokenizer(r\"echo $(cat <<HERE1 <<HERE2 | wc -l\nTEXT\nHERE1\nOTHER\nHERE2\n)\")?"
---
TokenizerResult(
  input: "echo $(cat <<HERE1 <<HERE2 | wc -l\nTEXT\nHERE1\nOTHER\nHERE2\n)",
  result: [
    W("echo", Loc(
      start: Pos(
        idx: 0,
        line: 1,
        col: 1,
      ),
      end: Pos(
        idx: 4,
        line: 1,
        col: 5,
      ),
    )),
    W("$(cat <<HERE1 <<HERE2 | wc -l\nTEXT\nHERE1\nOTHER\nHERE2\n)", Loc(
      start: Pos(
        idx: 5,
        line: 1,
        col: 6,
      ),
      end: Pos(
        idx: 59,
        line: 6,
        col: 2,
      ),
    )),
  ],
)
