//! XML parser for extracting tool calls from AI model responses

use crate::agent::{<PERSON><PERSON><PERSON><PERSON>, AgentR<PERSON>ult};
use quick_xml::Reader;
use quick_xml::events::Event;
use regex::Regex;
use std::collections::HashMap;

/// Represents a parsed tool call
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct ToolCall {
    /// Name of the tool to call
    pub tool_name: String,
    /// Parameters for the tool call
    pub parameters: HashMap<String, String>,
}

/// XML parser for extracting tool calls from AI responses
pub struct XmlParser {
    /// Regex for finding XML tool calls
    tool_call_regex: Regex,
}

impl XmlParser {
    /// Create a new XML parser
    pub fn new() -> AgentResult<Self> {
        // Use a simpler regex that doesn't require backreferences
        let tool_call_regex = Regex::new(r"<(\w+)>.*?</\w+>")
            .map_err(|e| AgentError::General(format!("Failed to compile regex: {}", e)))?;

        Ok(Self { tool_call_regex })
    }

    /// Extract the first tool call from the given text
    pub fn extract_tool_call(&self, text: &str) -> AgentResult<ToolCall> {
        // Use a more direct approach to find XML elements
        let tool_call = self.find_first_xml_element(text)?;
        Ok(tool_call)
    }

    /// Find the first XML element in the text
    fn find_first_xml_element(&self, text: &str) -> AgentResult<ToolCall> {
        // Look for opening tags
        let mut start_pos = 0;
        while let Some(open_start) = text[start_pos..].find('<') {
            let abs_open_start = start_pos + open_start;
            if let Some(open_end) = text[abs_open_start..].find('>') {
                let abs_open_end = abs_open_start + open_end;
                let tag_content = &text[abs_open_start + 1..abs_open_end];

                // Skip comments, declarations, and closing tags
                if tag_content.starts_with('!')
                    || tag_content.starts_with('?')
                    || tag_content.starts_with('/')
                {
                    start_pos = abs_open_end + 1;
                    continue;
                }

                // Extract tag name (before any spaces)
                let tool_name = tag_content
                    .split_whitespace()
                    .next()
                    .unwrap_or(tag_content)
                    .to_string();

                // Find the corresponding closing tag
                let closing_tag = format!("</{}>", tool_name);
                if let Some(close_pos) = text[abs_open_end..].find(&closing_tag) {
                    let abs_close_pos = abs_open_end + close_pos;
                    let xml_content = &text[abs_open_start..abs_close_pos + closing_tag.len()];

                    // Parse the XML content to extract parameters
                    let parameters = self.parse_tool_parameters(&tool_name, xml_content)?;

                    return Ok(ToolCall {
                        tool_name,
                        parameters,
                    });
                }
            }
            start_pos = abs_open_start + 1;
        }

        Err(AgentError::Xml(
            "No tool call found in response".to_string(),
        ))
    }

    /// Parse parameters from XML content for a specific tool
    fn parse_tool_parameters(
        &self,
        tool_name: &str,
        xml_content: &str,
    ) -> AgentResult<HashMap<String, String>> {
        let mut parameters = HashMap::new();
        let mut reader = Reader::from_str(xml_content);
        reader.config_mut().trim_text(true);

        let mut buf = Vec::new();
        let mut current_element = String::new();
        let mut current_text = String::new();
        let mut in_tool_element = false;

        loop {
            match reader.read_event_into(&mut buf) {
                Ok(Event::Start(ref e)) => {
                    let name = String::from_utf8_lossy(e.name().as_ref()).to_string();
                    if name == tool_name {
                        in_tool_element = true;
                    } else if in_tool_element {
                        if !current_element.is_empty() {
                            // We're starting a nested element, save current content
                            parameters
                                .insert(current_element.clone(), current_text.trim().to_string());
                        }
                        current_element = name;
                        current_text.clear();
                    }
                }
                Ok(Event::Text(ref e)) => {
                    if in_tool_element {
                        let text = e.unescape().map_err(|e| {
                            AgentError::Xml(format!("Failed to unescape text: {}", e))
                        })?;
                        if !current_element.is_empty() {
                            current_text.push_str(&text);
                        }
                    }
                }
                Ok(Event::End(ref e)) => {
                    let name = String::from_utf8_lossy(e.name().as_ref()).to_string();
                    if name == tool_name {
                        // Save any remaining content before breaking
                        if !current_element.is_empty() && !current_text.trim().is_empty() {
                            parameters
                                .insert(current_element.clone(), current_text.trim().to_string());
                        }
                        break;
                    } else if in_tool_element && name == current_element {
                        // For nested elements, collect all content including nested XML
                        let content = if current_text.trim().is_empty() {
                            // If no direct text content, this might be a container element
                            // For now, just mark it as present
                            "present".to_string()
                        } else {
                            current_text.trim().to_string()
                        };
                        parameters.insert(current_element.clone(), content);
                        current_element.clear();
                        current_text.clear();
                    }
                }
                Ok(Event::Eof) => break,
                Err(e) => {
                    return Err(AgentError::Xml(format!("XML parsing error: {}", e)));
                }
                _ => {}
            }
            buf.clear();
        }

        Ok(parameters)
    }

    /// Check if the text contains any tool calls
    pub fn contains_tool_call(&self, text: &str) -> bool {
        self.tool_call_regex.is_match(text)
    }

    /// Extract all tool calls from the text (returns the first one for now)
    pub fn extract_all_tool_calls(&self, text: &str) -> AgentResult<Vec<ToolCall>> {
        let tool_call = self.extract_tool_call(text)?;
        Ok(vec![tool_call])
    }
}

impl Default for XmlParser {
    fn default() -> Self {
        Self::new().expect("Failed to create XML parser")
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_extract_simple_tool_call() {
        let parser = XmlParser::new().unwrap();
        let text = r#"I need to ask a question.
        
<ask_question>
<question>What is your name?</question>
</ask_question>

That's my response."#;

        let tool_call = parser.extract_tool_call(text).unwrap();
        assert_eq!(tool_call.tool_name, "ask_question");
        assert_eq!(
            tool_call.parameters.get("question").unwrap(),
            "What is your name?"
        );
    }

    #[test]
    fn test_extract_complex_tool_call() {
        let parser = XmlParser::new().unwrap();
        let text = r#"<command_exec>
<command>ls -la</command>
<confirmed>true</confirmed>
</command_exec>"#;

        let tool_call = parser.extract_tool_call(text).unwrap();
        assert_eq!(tool_call.tool_name, "command_exec");
        assert_eq!(tool_call.parameters.get("command").unwrap(), "ls -la");
        assert_eq!(tool_call.parameters.get("confirmed").unwrap(), "true");
    }

    #[test]
    fn test_extract_final_answer() {
        let parser = XmlParser::new().unwrap();
        let text = r#"<final_answer>
<answer>The file has been created successfully.</answer>
<references>
<reference>/path/to/file</reference>
</references>
</final_answer>"#;

        let tool_call = parser.extract_tool_call(text).unwrap();
        assert_eq!(tool_call.tool_name, "final_answer");
        assert_eq!(
            tool_call.parameters.get("answer").unwrap(),
            "The file has been created successfully."
        );
        assert!(tool_call.parameters.get("references").is_some());
    }

    #[test]
    fn test_no_tool_call() {
        let parser = XmlParser::new().unwrap();
        let text = "This is just regular text without any tool calls.";

        let result = parser.extract_tool_call(text);
        assert!(result.is_err());
    }

    #[test]
    fn test_contains_tool_call() {
        let parser = XmlParser::new().unwrap();

        assert!(
            parser.contains_tool_call("<ask_question><question>Test</question></ask_question>")
        );
        assert!(!parser.contains_tool_call("No tool calls here"));
    }
}
