//! XML parser for extracting tool calls from AI model responses

use crate::agent::{<PERSON><PERSON><PERSON><PERSON>, AgentR<PERSON>ult};
use quick_xml::Reader;
use quick_xml::events::Event;
use regex::Regex;
use std::collections::HashMap;

/// Represents a parsed tool call
#[derive(<PERSON>bu<PERSON>, <PERSON>lone)]
pub struct ToolCall {
    /// Name of the tool to call
    pub tool_name: String,
    /// Parameters for the tool call
    pub parameters: HashMap<String, String>,
}

/// XML parser for extracting tool calls from AI responses
pub struct XmlParser {
    /// Regex for finding XML tool calls
    tool_call_regex: Regex,
}

impl XmlParser {
    /// Create a new XML parser
    pub fn new() -> AgentResult<Self> {
        let tool_call_regex = Regex::new(r"<(\w+)>(.*?)</\1>")
            .map_err(|e| AgentError::General(format!("Failed to compile regex: {}", e)))?;

        Ok(Self { tool_call_regex })
    }

    /// Extract the first tool call from the given text
    pub fn extract_tool_call(&self, text: &str) -> AgentResult<ToolCall> {
        // Find all potential tool calls using regex
        let captures: Vec<_> = self.tool_call_regex.captures_iter(text).collect();

        if captures.is_empty() {
            return Err(AgentError::Xml(
                "No tool call found in response".to_string(),
            ));
        }

        // Use the first capture
        let capture = &captures[0];
        let tool_name = capture.get(1).unwrap().as_str().to_string();
        let xml_content = capture.get(0).unwrap().as_str();

        // Parse the XML content to extract parameters
        let parameters = self.parse_tool_parameters(&tool_name, xml_content)?;

        Ok(ToolCall {
            tool_name,
            parameters,
        })
    }

    /// Parse parameters from XML content for a specific tool
    fn parse_tool_parameters(
        &self,
        tool_name: &str,
        xml_content: &str,
    ) -> AgentResult<HashMap<String, String>> {
        let mut parameters = HashMap::new();
        let mut reader = Reader::from_str(xml_content);
        reader.config_mut().trim_text(true);

        let mut buf = Vec::new();
        let mut current_element = String::new();
        let mut current_text = String::new();
        let mut in_tool_element = false;

        loop {
            match reader.read_event_into(&mut buf) {
                Ok(Event::Start(ref e)) => {
                    let name = String::from_utf8_lossy(e.name().as_ref()).to_string();
                    if name == tool_name {
                        in_tool_element = true;
                    } else if in_tool_element {
                        current_element = name;
                        current_text.clear();
                    }
                }
                Ok(Event::Text(ref e)) => {
                    if in_tool_element && !current_element.is_empty() {
                        let text = e.unescape().map_err(|e| {
                            AgentError::Xml(format!("Failed to unescape text: {}", e))
                        })?;
                        current_text.push_str(&text);
                    }
                }
                Ok(Event::End(ref e)) => {
                    let name = String::from_utf8_lossy(e.name().as_ref()).to_string();
                    if name == tool_name {
                        break;
                    } else if in_tool_element && name == current_element {
                        parameters.insert(current_element.clone(), current_text.trim().to_string());
                        current_element.clear();
                        current_text.clear();
                    }
                }
                Ok(Event::Eof) => break,
                Err(e) => {
                    return Err(AgentError::Xml(format!("XML parsing error: {}", e)));
                }
                _ => {}
            }
            buf.clear();
        }

        Ok(parameters)
    }

    /// Check if the text contains any tool calls
    pub fn contains_tool_call(&self, text: &str) -> bool {
        self.tool_call_regex.is_match(text)
    }

    /// Extract all tool calls from the text (returns the first one for now)
    pub fn extract_all_tool_calls(&self, text: &str) -> AgentResult<Vec<ToolCall>> {
        let tool_call = self.extract_tool_call(text)?;
        Ok(vec![tool_call])
    }
}

impl Default for XmlParser {
    fn default() -> Self {
        Self::new().expect("Failed to create XML parser")
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_extract_simple_tool_call() {
        let parser = XmlParser::new().unwrap();
        let text = r#"I need to ask a question.
        
<ask_question>
<question>What is your name?</question>
</ask_question>

That's my response."#;

        let tool_call = parser.extract_tool_call(text).unwrap();
        assert_eq!(tool_call.tool_name, "ask_question");
        assert_eq!(
            tool_call.parameters.get("question").unwrap(),
            "What is your name?"
        );
    }

    #[test]
    fn test_extract_complex_tool_call() {
        let parser = XmlParser::new().unwrap();
        let text = r#"<command_exec>
<command>ls -la</command>
<confirmed>true</confirmed>
</command_exec>"#;

        let tool_call = parser.extract_tool_call(text).unwrap();
        assert_eq!(tool_call.tool_name, "command_exec");
        assert_eq!(tool_call.parameters.get("command").unwrap(), "ls -la");
        assert_eq!(tool_call.parameters.get("confirmed").unwrap(), "true");
    }

    #[test]
    fn test_extract_final_answer() {
        let parser = XmlParser::new().unwrap();
        let text = r#"<final_answer>
<answer>The file has been created successfully.</answer>
<references>
<reference>/path/to/file</reference>
</references>
</final_answer>"#;

        let tool_call = parser.extract_tool_call(text).unwrap();
        assert_eq!(tool_call.tool_name, "final_answer");
        assert_eq!(
            tool_call.parameters.get("answer").unwrap(),
            "The file has been created successfully."
        );
        assert!(tool_call.parameters.get("references").is_some());
    }

    #[test]
    fn test_no_tool_call() {
        let parser = XmlParser::new().unwrap();
        let text = "This is just regular text without any tool calls.";

        let result = parser.extract_tool_call(text);
        assert!(result.is_err());
    }

    #[test]
    fn test_contains_tool_call() {
        let parser = XmlParser::new().unwrap();

        assert!(
            parser.contains_tool_call("<ask_question><question>Test</question></ask_question>")
        );
        assert!(!parser.contains_tool_call("No tool calls here"));
    }
}
