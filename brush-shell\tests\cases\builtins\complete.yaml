name: "Builtins: complete"
cases:
  - name: "complete with no options"
    stdin: |
      complete -W foo mycmd
      complete

  - name: "Roundtrip: complete -W"
    stdin: |
      complete -W foo mycmd
      complete -p mycmd

      complete -W 'foo bar' mycmd
      complete -p mycmd

  - name: "Roundtrip: complete -P"
    stdin: |
      complete -P myprefix mycmd
      complete -p mycmd

      complete -P 'my prefix' mycmd
      complete -p mycmd

  - name: "Roundtrip: complete -S"
    stdin: |
      complete -S mysuffix mycmd
      complete -p mycmd

      complete -S 'my suffix' mycmd
      complete -p mycmd

  - name: "Roundtrip: complete -F"
    stdin: |
      complete -Fmyfunc mycmd
      complete -p mycmd

  - name: "Roundtrip: complete -F"
    stdin: |
      complete -G pattern mycmd
      complete -p mycmd

      complete -G 'pat tern' mycmd
      complete -p mycmd

  - name: "Roundtrip: complete -X"
    stdin: |
      complete -X pattern mycmd
      complete -p mycmd

      complete -X 'pat tern' mycmd
      complete -p mycmd

  - name: "Roundtrip: complete -C"
    stdin: |
      complete -C cmd mycmd
      complete -p mycmd

      complete -C 'c md' mycmd
      complete -p mycmd

  - name: "Roundtrip: complete -A"
    stdin: |
      for action in alias arrayvar binding builtin command directory disabled enabled export file 'function' group helptopic hostname job keyword running service setopt shopt signal stopped user variable; do
        complete -A ${action} mycmd
        complete -p mycmd
      done

  - name: "Roundtrip: complete -o options"
    stdin: |
      for opt in bashdefault default dirnames filenames noquote nosort nospace plusdirs; do
        echo "--- Testing option: ${opt} ------------------"
        complete -o ${opt} mycmd_${opt}
        complete -p mycmd_${opt}
      done

  - name: "complete -r"
    stdin: |
      echo "[Removing non-existent]"
      complete -r nonexistent
      echo $?

      echo "[Removing existing]"
      complete -W token mycmd
      complete -r mycmd
      echo $?
      complete -p mycmd 2>/dev/null

  - name: "complete -r with no args"
    stdin: |
      complete -W token mycmd1
      complete -W token mycmd2

      complete -r
      echo $?

      complete -p

  - name: "complete -r with special options"
    stdin: |
      complete -W token mycmd
      complete -W other -E

      complete -r -E
      echo $?
      
      complete -p
