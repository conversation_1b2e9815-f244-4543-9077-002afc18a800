name: "Builtins: type"
cases:
  - name: "Test type with no arguments"
    stdin: type

  - name: "Test type with a valid command"
    stdin: type ls

  - name: "Test type with an invalid command"
    ignore_stderr: true
    stdin: type invalid_command

  - name: "Test type with -t option and a builtin command"
    stdin: type -t cd

  - name: "Test type with -t option and an external command"
    stdin: type -t ls

  - name: "Test type with -t option and an undefined command"
    ignore_stderr: true
    stdin: type -t undefined_command

  - name: "Test type with -a option and a command with multiple definitions"
    stdin: type -a true

  - name: Test type with -p option and a builtin command
    stdin: type -p cd

  - name: Test type with -p option and an external command
    stdin: type -p ls

  - name: Test type with -P option and a builtin command
    stdin: type -P cd

  - name: Test type with -P option and an external command
    stdin: type -P ls

  - name: Test type with -f option and a function
    ignore_stderr: true
    stdin: |
      function myfunc() { echo "Hello, world!"; }
      type -f myfunc

  - name: Test type with -f option and a command
    stdin: type -f ls

  - name: Test type with -a option and a function
    stdin: |
      function myfunc() { echo "Hello, world!"; }
      type -a myfunc

  - name: Test type with hashed path
    stdin: |
      hash -p /some/ls ls
      type ls

  - name: Test type -a with hashed path
    stdin: |
      hash -p /some/ls ls
      type -a ls

  - name: Test type -P with hashed path
    stdin: |
      hash -p /some/ls ls
      type -P ls

  - name: Test type -P -a with hashed path
    min_oracle_version: 5.3 # Behavior changed in bash 5.3
    stdin: |
      hash -p /some/ls ls
      type -P -a ls

  - name: Test type -p -a with hashed path
    stdin: |
      hash -p /some/ls ls
      type -p -a ls
