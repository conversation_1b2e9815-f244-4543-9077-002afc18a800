name: "Builtins: shopt"
cases:
  - name: "shopt defaults"
    min_oracle_version: 5.2
    known_failure: true # TODO: new options from newer version of bash?
    stdin: |
      shopt | sort | grep -v extglob

  - name: "shopt interactive defaults"
    min_oracle_version: 5.2
    known_failure: true # TODO: new options from newer version of bash?
    pty: true
    args: ["-i", "-c", "shopt | sort | grep -v extglob"]

  - name: "shopt -o defaults"
    min_oracle_version: 5.3 # Spacing changed in bash 5.3
    stdin: |
      shopt -o | sort

  - name: "shopt -o interactive defaults"
    min_oracle_version: 5.3 # Spacing changed in bash 5.3
    pty: true
    args: ["-i", "-c", "shopt -o | sort | grep -v monitor"]

  - name: "extglob defaults"
    known_failure: true # TODO: we force this setting on in our shell
    stdin: |
      shopt extglob

  - name: "extglob interactive defaults"
    pty: true
    args: ["-i", "-c", "shopt extglob"]
    known_failure: true

  - name: "shopt -o interactive monitor default"
    min_oracle_version: 5.3 # Spacing changed in bash 5.3
    pty: true
    args: ["-i", "-c", "shopt -o monitor"]

  - name: "shopt toggle"
    min_oracle_version: 5.3 # Spacing changed in bash 5.3
    stdin: |
      echo "Setting checkwinsize"
      shopt -s checkwinsize

      echo "Displaying checkwinsize"
      shopt checkwinsize
      shopt -p checkwinsize

      echo "Unsetting checkwinsize"
      shopt -u checkwinsize

      echo "Displaying checkwinsize"
      shopt checkwinsize
      shopt -p checkwinsize

  - name: "shopt -o usage"
    min_oracle_version: 5.3 # Spacing changed in bash 5.3
    stdin: |
      echo "Setting emacs"
      shopt -o -s emacs

      echo "Displaying emacs"
      shopt -o emacs
      shopt -o -p emacs

      echo "Unsetting emacs"
      shopt -o -u emacs

      echo "Displaying emacs"
      shopt -o emacs
      shopt -o -p emacs

  - name: "shopt -s lastpipe"
    stdin: |
      echo ignored | var=value
      echo "1. var='${var}'"

      shopt -s lastpipe
      set +o monitor
      echo ignored | var=value
      echo "2. var='${var}'"
