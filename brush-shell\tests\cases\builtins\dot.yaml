name: "Builtins: dot"
cases:
  - name: "Basic dot usage"
    test_files:
      - path: "script.sh"
        contents: |
          echo "In sourced script"
          var="updated"
    stdin: |
      var="orig"
      . script.sh
      echo "var: ${var}"

  - name: "Basic source usage"
    test_files:
      - path: "script.sh"
        contents: |
          echo "In sourced script"
          for f in ${FUNCNAME[@]}; do
            echo "FUNCNAME: '${f}'"
          done
          for s in ${BASH_SOURCE[@]}; do
            echo "BASH_SOURCE: '${s}'"
          done
          var="updated"

          function script_func() {
            echo "In script func"
            for f in ${FUNCNAME[@]}; do
              echo "FUNCNAME: '${f}'"
            done
            for s in ${BASH_SOURCE[@]}; do
              echo "BASH_SOURCE: '${s}'"
            done
          }
    stdin: |
      var="orig"
      source ./script.sh
      echo "var: ${var}"
      script_func

  - name: "Source non-existent file path"
    ignore_stderr: true
    stdin: |
      source script.sh
      echo "Result: $?"

  - name: "Source dir"
    ignore_stderr: true
    stdin: |
      source .
      echo "Result: $?"

  - name: "Source script without args"
    test_files:
      - path: "script.sh"
        contents: |
          echo "In sourced script"
          echo "Args: $@"
    stdin: |
      set -- outer1 outer2 outer3
      source script.sh

  - name: "Source script with args"
    test_files:
      - path: "script.sh"
        contents: |
          echo "In sourced script"
          echo "Args: $@"
    stdin: |
      set -- outer1 outer2 outer3
      source script.sh inner1 inner2

  - name: "Source with redirection"
    test_files:
      - path: "script.sh"
        contents: |
          echo "In sourced script"
    stdin: |
      source script.sh arg1 arg2 > out.txt
      echo "Sourced script; dumping..."
      cat out.txt
