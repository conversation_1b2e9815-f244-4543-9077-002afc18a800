name: "Special parameters"
cases:
  - name: "$@ and $*"
    stdin: |
      function myfunc() {
          echo "--myfunc called--"

          echo "COUNT: $#"
          echo "AT-SIGN VALUE: '$@'"
          echo "STAR VALUE: '$*'"

          for arg in $@; do
              echo "AT-SIGN ELEMENT: '${arg}'"
          done

          for arg in $*; do
              echo "STAR ELEMENT: '${arg}'"
          done

          for arg in "$@"; do
            echo "DOUBLE-QUOTED AT-SIGN ELEMENT: '${arg}'"
          done

          for arg in "$*"; do
            echo "DOUBLE-QUOTED STAR ELEMENT: '${arg}'"
          done
      }

      myfunc
      myfunc 1
      myfunc 1 2
      myfunc "a b c" 2
