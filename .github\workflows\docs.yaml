name: "Docs"
on:
  workflow_dispatch:
  pull_request:
  push:
    branches:
      - main

env:
  RUST_BACKTRACE: 1
  CARGO_TERM_COLOR: always
  CLICOLOR: 1
  CLICOLOR_FORCE: 1

permissions:
  contents: read

jobs:
  # Check links in docs
  check-links:
    name: "Check links"
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout sources
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          persist-credentials: false

      - name: Check repo-local links
        uses: lycheeverse/lychee-action@5c4ee84814c983aa7164eaee476f014e53ff3963 # v2.5.0
        with:
          args: ". --offline --verbose --no-progress"
          fail: true

  # Generate docs content
  generate-docs:
    name: "Generate usage docs"
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          persist-credentials: false

      - name: Set up rust toolchain
        uses: dtolnay/rust-toolchain@b3b07ba8b418998c39fb20f53e8b695cdcc8de1b # v1
        with:
          toolchain: stable

      - name: Enable cargo cache
        uses: Swatinem/rust-cache@98c8021b550208e191a6a3145459bfc9fb29c4c0 # v2.8.0

      - name: Create dirs
        run: mkdir -p ./md ./man

      - name: Build markdown info
        run: cargo xtask generate-markdown --out ./md/brush.md

      - name: Upload markdown docs
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: docs-markdown
          path: md

      - name: Build man pages
        run: cargo xtask generate-man --output-dir ./man

      - name: Upload man pages
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: docs-man
          path: man
