//! AI Agent module for processing user commands through AI models
//!
//! This module provides an AI-powered agent that can process user commands
//! when they are not valid shell commands, using OpenAI-compatible APIs
//! and a set of tools to interact with the system.

pub mod config;
pub mod conversation_manager;
pub mod core;
pub mod security;
pub mod tool_executor;
pub mod tools;
pub mod xml_parser;

#[cfg(test)]
mod tests;

pub use config::AgentConfig;
pub use conversation_manager::ConversationManager;
pub use core::{Agent, ProcessResult};
pub use tool_executor::ToolExecutor;

use crate::ShellError;

/// Result type for agent operations
pub type AgentResult<T> = Result<T, AgentError>;

/// Errors that can occur during agent operations
#[derive(thiserror::Error, Debug)]
pub enum AgentError {
    /// Configuration error
    #[error("Configuration error: {0}")]
    Config(String),

    /// HTTP request failed
    #[error("HTTP request failed: {0}")]
    Http(#[from] reqwest::Error),

    /// JSON parsing failed
    #[error("JSON parsing failed: {0}")]
    Json(#[from] serde_json::Error),

    /// XML parsing failed
    #[error("XML parsing failed: {0}")]
    Xml(String),

    /// Tool execution failed
    #[error("Tool execution failed: {0}")]
    ToolExecution(String),

    /// Shell error
    #[error("Shell error: {0}")]
    Shell(#[from] ShellError),

    /// IO error
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),

    /// General agent error
    #[error("Agent error: {0}")]
    General(String),
}

impl From<AgentError> for ShellError {
    fn from(err: AgentError) -> Self {
        ShellError::General(err.to_string())
    }
}
