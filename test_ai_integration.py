#!/usr/bin/env python3
"""
测试AI Agent集成的脚本
"""

import subprocess
import time
import sys
import os

def test_ai_integration():
    """测试AI Agent集成"""
    print("🤖 测试AI Agent集成...")
    
    # 创建一个测试脚本来模拟用户输入
    test_script = """
echo "测试开始"
invalid_command_that_should_trigger_ai
echo "测试结束"
exit
"""
    
    try:
        # 运行brush shell并提供输入
        process = subprocess.Popen(
            ["./target/debug/brush.exe"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=os.getcwd()
        )
        
        # 发送测试命令
        stdout, stderr = process.communicate(input=test_script, timeout=10)
        
        print("标准输出:")
        print(stdout)
        print("\n标准错误:")
        print(stderr)
        
        # 检查是否包含AI处理的消息
        if "AiShell正在处理" in stdout or "AI Agent" in stdout:
            print("✅ AI Agent集成成功！检测到AI处理消息")
            return True
        else:
            print("❌ 未检测到AI Agent处理消息")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 测试超时")
        process.kill()
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_config_loading():
    """测试配置加载"""
    print("\n📋 测试配置加载...")
    
    # 创建测试配置文件
    config_content = '''
{
  "openai": {
    "base_url": "https://api.openai.com/v1",
    "model_id": "gpt-4",
    "api_key": "test-key",
    "max_retries": 3,
    "timeout_seconds": 30
  },
  "tools": {
    "enabled_tools": ["ask_question", "final_answer"]
  }
}
'''
    
    try:
        with open("test_config.json", "w") as f:
            f.write(config_content)
        
        print("✅ 测试配置文件创建成功")
        
        # 清理
        os.remove("test_config.json")
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始AI Agent集成测试\n")
    
    tests = [
        ("AI Agent集成测试", test_ai_integration),
        ("配置加载测试", test_config_loading),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 AI Agent集成测试通过！")
        print("\n📋 使用说明:")
        print("1. 设置环境变量: export OPENAI_API_KEY='your-api-key'")
        print("2. 启动shell: ./target/debug/brush.exe")
        print("3. 输入无效命令测试AI Agent: 'help me create a file'")
        print("4. AI Agent会显示处理消息")
        return 0
    else:
        print("⚠️ 部分测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
