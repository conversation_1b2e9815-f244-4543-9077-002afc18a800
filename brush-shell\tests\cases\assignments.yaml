name: "Assignments"
cases:
  - name: "First char is equals sign"
    ignore_stderr: true
    stdin: |
      =x

  - name: "Basic assignment"
    stdin: |
      x=yz
      echo "x: ${x}"

  - name: "Invalid variable name"
    ignore_stderr: true
    stdin: |
      @=something

  - name: "Quoted equals sign"
    ignore_stderr: true
    stdin: |
      x"="3

  - name: "Multiple equals signs"
    stdin: |
      x=y=z
      echo "x: ${x}"

  - name: "Assignment with tilde expansion"
    known_failure: true
    stdin: |
      HOME=/some/dir

      var=~/file1.txt
      echo "~/file1.txt: ${var}"

      var=~/file1.txt:~/file2.txt
      echo "~/file1.txt:~/file2.txt: ${var}"
