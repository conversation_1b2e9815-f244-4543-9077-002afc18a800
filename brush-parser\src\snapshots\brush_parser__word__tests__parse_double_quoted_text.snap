---
source: brush-parser/src/word.rs
expression: "test_parse(r#\"\"a ${b} c\"\"#)?"
---
ParseTestResults(
  input: "\"a ${b} c\"",
  result: [
    WordPieceWithSource(
      piece: DoubleQuotedSequence([
        WordPieceWithSource(
          piece: Text("a "),
          start_index: 1,
          end_index: 3,
        ),
        WordPieceWithSource(
          piece: ParameterExpansion(Parameter(
            parameter: Named("b"),
            indirect: false,
          )),
          start_index: 3,
          end_index: 7,
        ),
        WordPieceWithSource(
          piece: Text(" c"),
          start_index: 7,
          end_index: 9,
        ),
      ]),
      start_index: 0,
      end_index: 10,
    ),
  ],
)
