name: "Builtins: builtin"
cases:
  - name: "builtin with no builtin"
    stdin: builtin

  - name: "builtin with unknown builtin"
    ignore_stderr: true
    stdin: builtin not-a-builtin args

  - name: "valid builtin"
    stdin: builtin echo "Hello world"

  - name: "valid builtin with hyphen args"
    stdin: builtin echo -e "Hello\nWorld"

  - name: "builtin passing through results"
    stdin: |
      builtin false; echo "builtin false => $?"
      builtin true; echo "builtin true => $?"

  - name: "builtin with non-decl builtin"
    stdin: |
      builtin echo variable=value

  - name: "builtin with decl builtin"
    stdin: |
      builtin declare variable=value
      declare -p variable
