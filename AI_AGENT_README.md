# Brush Shell AI Agent

这个AI Agent为Brush Shell添加了智能命令处理功能。当用户输入的命令无法被shell识别时，AI Agent会接管处理，通过调用各种工具来完成用户的请求。

## 功能特性

### 核心功能
- **智能命令处理**：当shell无法识别命令时，自动调用AI Agent处理
- **工具系统**：支持多种工具，包括文件操作、网络请求、系统命令等
- **安全控制**：内置命令验证和用户确认机制
- **对话管理**：维护对话历史，支持上下文理解

### 支持的工具
1. **基础工具**
   - `command_exec`: 执行系统命令
   - `list_dir`: 列出目录内容
   - `read_file`: 读取文件内容
   - `write_file`: 写入文件内容
   - `search_files`: 在文件中搜索内容

2. **高级工具**
   - `ask_question`: 向用户询问更多信息
   - `final_answer`: 提供最终答案
   - `update_todo_list`: 管理待办事项
   - `text_process`: 文本处理和分析

3. **网络工具**
   - `web_fetch`: 获取网页内容（支持JavaScript渲染）
   - `http_request`: 发送HTTP请求

## 配置说明

### 1. 基本配置

创建配置文件 `~/.config/brush/agent_config.json`：

```json
{
  "openai": {
    "base_url": "https://api.openai.com/v1",
    "model_id": "gpt-4",
    "api_key": "your-api-key-here",
    "max_retries": 3,
    "timeout_seconds": 30
  }
}
```

### 2. 环境变量配置

也可以通过环境变量设置API密钥：

```bash
export OPENAI_API_KEY="your-api-key-here"
```

### 3. 完整配置示例

参考项目根目录的 `agent_config.json` 文件获取完整配置示例。

## 使用方法

### 1. 启动Shell

正常启动brush shell：

```bash
brush
```

### 2. 使用AI Agent

当输入无法识别的命令时，AI Agent会自动接管：

```bash
# 这些命令会被AI Agent处理
brush> 帮我创建一个名为test.txt的文件，内容是"Hello World"
brush> 查找当前目录下包含"TODO"的文件
brush> 获取https://example.com的网页内容
brush> 分析这个文件的内容：README.md
```

### 3. 示例对话

```bash
brush> 帮我创建一个Python项目结构
AiShell正在处理：帮我创建一个Python项目结构

我来帮您创建一个标准的Python项目结构。

[AI Agent会调用相应的工具创建目录和文件]

项目结构已创建完成：
- src/
- tests/
- requirements.txt
- setup.py
- README.md
```

## 安全特性

### 1. 命令验证
- **白名单**：安全命令自动执行
- **黑名单**：危险命令被阻止
- **确认机制**：未知命令需要用户确认

### 2. 安全配置
```json
{
  "security": {
    "require_confirmation": true,
    "always_confirm": ["rm", "mv", "chmod"],
    "always_block": ["rm -rf /", "shutdown"]
  }
}
```

## 工具开发

### 1. 创建自定义工具

实现 `Tool` trait：

```rust
use crate::agent::tools::{Tool, ToolResult};

pub struct MyCustomTool;

impl Tool for MyCustomTool {
    fn name(&self) -> &str {
        "my_tool"
    }
    
    fn description(&self) -> &str {
        "My custom tool description"
    }
    
    fn execute(&self, parameters: HashMap<String, String>) -> AgentResult<ToolResult> {
        // 工具实现逻辑
        Ok(ToolResult::success("Tool executed successfully".to_string()))
    }
}
```

### 2. 注册工具

在 `ToolExecutor` 中注册：

```rust
executor.register_tool(Arc::new(MyCustomTool));
```

## 故障排除

### 1. AI Agent未初始化
- 检查API密钥是否正确设置
- 确认网络连接正常
- 查看日志输出：`RUST_LOG=debug brush`

### 2. 工具执行失败
- 检查工具参数是否正确
- 确认有足够的系统权限
- 查看详细错误信息

### 3. 网络工具问题
- 确认目标URL可访问
- 检查防火墙设置
- 对于需要JavaScript的页面，确保Chrome/Chromium可用

## 开发和测试

### 1. 编译项目

```bash
cargo build --release
```

### 2. 运行测试

```bash
cargo test
```

### 3. 启用调试日志

```bash
RUST_LOG=debug ./target/release/brush
```

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 许可证

本项目采用MIT许可证。详见LICENSE文件。
