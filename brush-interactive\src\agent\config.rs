//! Configuration management for the AI Agent

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;

/// Main configuration for the AI Agent
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AgentConfig {
    /// OpenAI API configuration
    pub openai: OpenAIConfig,
    
    /// System prompt template configuration
    pub prompt: PromptConfig,
    
    /// Tool configuration
    pub tools: ToolConfig,
    
    /// Security configuration
    pub security: SecurityConfig,
    
    /// Web fetching configuration
    pub web_fetch: Option<WebFetchConfig>,
}

/// OpenAI API configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OpenAIConfig {
    /// Base URL for the API (e.g., "https://api.openai.com/v1")
    pub base_url: String,
    
    /// Model ID to use (e.g., "gpt-4", "claude-3-sonnet")
    pub model_id: String,
    
    /// API key for authentication
    pub api_key: String,
    
    /// Maximum number of retries for API calls
    #[serde(default = "default_max_retries")]
    pub max_retries: u32,
    
    /// Timeout for API calls in seconds
    #[serde(default = "default_timeout")]
    pub timeout_seconds: u64,
}

/// System prompt template configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PromptConfig {
    /// Path to the system prompt template file
    pub template_path: Option<PathBuf>,
    
    /// Inline template string (used if template_path is not provided)
    pub template: Option<String>,
    
    /// Tool descriptions to include in the prompt
    pub tool_descriptions: HashMap<String, ToolDescription>,
}

/// Tool description for the prompt template
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolDescription {
    /// Description of what the tool does
    pub description: String,
    
    /// Example usage of the tool
    pub example: String,
    
    /// Requirements and constraints for using the tool
    pub requirements: String,
}

/// Tool configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolConfig {
    /// List of enabled tools
    pub enabled_tools: Vec<String>,
    
    /// Tool-specific configurations
    pub tool_configs: HashMap<String, serde_json::Value>,
}

/// Security configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityConfig {
    /// Path to command whitelist file
    pub whitelist_path: Option<PathBuf>,
    
    /// Path to command blacklist file
    pub blacklist_path: Option<PathBuf>,
    
    /// Whether to require confirmation for unknown commands
    #[serde(default = "default_require_confirmation")]
    pub require_confirmation: bool,
    
    /// Commands that always require confirmation
    pub always_confirm: Vec<String>,
    
    /// Commands that are always blocked
    pub always_block: Vec<String>,
}

/// Web fetching configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WebFetchConfig {
    /// Path to Chrome/Chromium executable
    pub chrome_path: Option<PathBuf>,
    
    /// Timeout for web requests in seconds
    #[serde(default = "default_web_timeout")]
    pub timeout_seconds: u64,
    
    /// Maximum content length to fetch
    #[serde(default = "default_max_content_length")]
    pub max_content_length: usize,
}

impl Default for AgentConfig {
    fn default() -> Self {
        Self {
            openai: OpenAIConfig::default(),
            prompt: PromptConfig::default(),
            tools: ToolConfig::default(),
            security: SecurityConfig::default(),
            web_fetch: Some(WebFetchConfig::default()),
        }
    }
}

impl Default for OpenAIConfig {
    fn default() -> Self {
        Self {
            base_url: "https://api.openai.com/v1".to_string(),
            model_id: "gpt-4".to_string(),
            api_key: std::env::var("OPENAI_API_KEY").unwrap_or_default(),
            max_retries: default_max_retries(),
            timeout_seconds: default_timeout(),
        }
    }
}

impl Default for PromptConfig {
    fn default() -> Self {
        Self {
            template_path: None,
            template: Some(default_system_template()),
            tool_descriptions: default_tool_descriptions(),
        }
    }
}

impl Default for ToolConfig {
    fn default() -> Self {
        Self {
            enabled_tools: vec![
                "command_exec".to_string(),
                "list_dir".to_string(),
                "read_file".to_string(),
                "search_files".to_string(),
                "ask_question".to_string(),
                "web_fetch".to_string(),
                "update_todo_list".to_string(),
                "final_answer".to_string(),
                "write_file".to_string(),
            ],
            tool_configs: HashMap::new(),
        }
    }
}

impl Default for SecurityConfig {
    fn default() -> Self {
        Self {
            whitelist_path: None,
            blacklist_path: None,
            require_confirmation: default_require_confirmation(),
            always_confirm: vec![
                "rm".to_string(),
                "rmdir".to_string(),
                "mv".to_string(),
                "cp".to_string(),
                "chmod".to_string(),
                "chown".to_string(),
            ],
            always_block: vec![
                "rm -rf /".to_string(),
                "shutdown".to_string(),
                "reboot".to_string(),
                "halt".to_string(),
            ],
        }
    }
}

impl Default for WebFetchConfig {
    fn default() -> Self {
        Self {
            chrome_path: None,
            timeout_seconds: default_web_timeout(),
            max_content_length: default_max_content_length(),
        }
    }
}

// Default value functions
fn default_max_retries() -> u32 { 3 }
fn default_timeout() -> u64 { 30 }
fn default_require_confirmation() -> bool { true }
fn default_web_timeout() -> u64 { 30 }
fn default_max_content_length() -> usize { 50000 }

fn default_system_template() -> String {
    r#"你是一个智能的命令行助手，能够帮助用户执行各种系统操作。

当前环境信息：
{ENVIRONMENT_DETAIL}

你可以使用以下工具：
{TOOL_DESCRIPTIONS}

请根据用户的输入选择合适的工具来完成任务。每次只能调用一个工具。
如果没有合适的工具，请使用ask_question工具询问更多信息，或使用final_answer工具给出最终回答。

重要要求：
1. 必须使用工具调用，不能直接回答
2. 每次只能调用一个工具
3. 工具调用必须使用XML格式
4. 对于危险操作，要谨慎处理并询问确认"#.to_string()
}

fn default_tool_descriptions() -> HashMap<String, ToolDescription> {
    let mut descriptions = HashMap::new();
    
    descriptions.insert("ask_question".to_string(), ToolDescription {
        description: "当需要特定信息才能继续时使用（用户未提供足够细节时）".to_string(),
        example: r#"<ask_question>
<question>您遇到问题的设备序列号是多少？</question>
</ask_question>"#.to_string(),
        requirements: "尽早提问，获取必要信息".to_string(),
    });
    
    descriptions.insert("final_answer".to_string(), ToolDescription {
        description: "当确认掌握足够信息时给出最终答复".to_string(),
        example: r#"<final_answer>
<answer>错误代码EC205表示电池过热，建议暂停使用并检查散热孔</answer>
<references>
<reference>/docs/error_codes/EC205</reference>
</references>
</final_answer>"#.to_string(),
        requirements: "提供结构化解决方案和相关参考".to_string(),
    });
    
    descriptions
}

impl AgentConfig {
    /// Load configuration from a file
    pub fn load_from_file(path: &std::path::Path) -> crate::agent::AgentResult<Self> {
        let content = std::fs::read_to_string(path)?;
        let config: Self = serde_json::from_str(&content)?;
        Ok(config)
    }
    
    /// Save configuration to a file
    pub fn save_to_file(&self, path: &std::path::Path) -> crate::agent::AgentResult<()> {
        let content = serde_json::to_string_pretty(self)?;
        std::fs::write(path, content)?;
        Ok(())
    }
    
    /// Load configuration from environment variables and default config file
    pub fn load() -> Self {
        // Try to load from default config file
        if let Some(config_dir) = dirs::config_dir() {
            let config_path = config_dir.join("brush").join("agent_config.json");
            if config_path.exists() {
                if let Ok(config) = Self::load_from_file(&config_path) {
                    return config;
                }
            }
        }
        
        // Fall back to default configuration
        Self::default()
    }
}
