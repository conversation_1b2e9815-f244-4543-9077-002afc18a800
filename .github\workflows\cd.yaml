#
# Based on https://github.com/release-plz/release-plz/blob/a5043c478d46d051c00e4fbc85036ac22510f07e/.github/workflows/cd.yml
#

name: CD # Continuous Deployment
run-name: CD${{ github.event_name == 'release' && ' (release)' || ' (dry run)' }}

on:
  release:
    types: [published]

  # Manual triggers don't actually publish but dry-run the builds.
  workflow_dispatch: null

  # Run on PR in dry-run mode to make sure this workflow is still generally
  # working.
  pull_request:
    branches: ["main"]

env:
  CARGO_INCREMENTAL: 0
  CARGO_NET_GIT_FETCH_WITH_CLI: true
  CARGO_NET_RETRY: 10
  CARGO_TERM_COLOR: always
  RUST_BACKTRACE: 1
  RUSTFLAGS: -D warnings
  RUSTUP_MAX_RETRIES: 10

defaults:
  run:
    shell: bash

permissions: {}

jobs:
  upload-assets:
    name: ${{ matrix.target }}

    permissions:
      contents: write
      id-token: write
      attestations: write

    if: github.event_name == 'workflow_dispatch' || github.event_name == 'pull_request' || (github.event_name == 'release' && github.repository_owner == 'reubeno' && startsWith(github.event.release.tag_name, 'brush-shell-v'))
    runs-on: ${{ matrix.os }}
    strategy:
      # Run all jobs to completion regardless of errors.
      # This is useful because sometimes we fail to compile for a certain target.
      fail-fast: false
      matrix:
        include:
          # NOTE: We don't provide Windows binaries yet.
          # - target: aarch64-pc-windows-msvc
          #   os: windows-2022
          - target: x86_64-unknown-linux-gnu
            os: ubuntu-22.04
          - target: x86_64-apple-darwin
            os: macos-13
          # - target: x86_64-pc-windows-msvc
          #   os: windows-2022
          - target: x86_64-unknown-linux-musl
            os: ubuntu-22.04
          - target: aarch64-unknown-linux-gnu
            os: ubuntu-22.04
          - target: aarch64-unknown-linux-musl
            os: ubuntu-22.04
          - target: aarch64-apple-darwin
            os: macos-13
    timeout-minutes: 60

    steps:
      - name: "Checkout repository"
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          persist-credentials: false

      - name: "Install Rust toolchain"
        uses: actions-rust-lang/setup-rust-toolchain@fb51252c7ba57d633bc668f941da052e410add48 # v1.13.0

      - name: "Setup cross-compiling toolchain"
        if: startsWith(matrix.os, 'ubuntu') && !contains(matrix.target, '-musl')
        uses: taiki-e/setup-cross-toolchain-action@4c366193ea262ae0c17c6b79c3edc3d4f1394263 # v1.30.0
        with:
          target: ${{ matrix.target }}

      - name: "Install musl cross tools"
        if: contains(matrix.target, '-musl')
        uses: taiki-e/install-action@5f6f3e0538d249cb0b47f8d5b636c120babeb082 # v2.58.6
        with:
          tool: cross

      - name: "Install cargo-about"
        uses: taiki-e/install-action@5f6f3e0538d249cb0b47f8d5b636c120babeb082 # v2.58.6
        with:
          tool: cargo-about

      - name: "Generate license notices"
        run: cd brush-shell && cargo about generate -o ../THIRD_PARTY_LICENSES.html about.hbs

      - name: "Update build flags"
        if: endsWith(matrix.target, 'windows-msvc')
        run: echo "RUSTFLAGS=${RUSTFLAGS} -C target-feature=+crt-static" >> "${GITHUB_ENV}"

      - name: "Build and upload binaries to release"
        uses: taiki-e/upload-rust-binary-action@3962470d6e7f1993108411bc3f75a135ec67fc8c # v1.27.0
        id: upload-release
        with:
          dry-run: ${{ github.event_name != 'release' }}
          bin: brush
          locked: true
          target: ${{ matrix.target }}
          tar: unix
          zip: windows
          checksum: sha256,sha512
          token: ${{ secrets.GITHUB_TOKEN }}
          include: "LICENSE,THIRD_PARTY_LICENSES.html"

      - name: "Generate artifact attestation"
        if: github.event_name == 'release'
        uses: actions/attest-build-provenance@e8998f949152b193b063cb0ec769d69d929409be # v2.4.0
        with:
          subject-path: "${{ steps.upload-release.outputs.archive }}.*"
