name: "Builtins: exec"
cases:
  - name: "Exec an invalid path"
    ignore_stderr: true
    stdin: |
      exec /some/nonexistent/path

  - name: "Exec with no arguments"
    stdin: |
      pid=$$
      exec
      [[ "${pid}" == "$$" ]] || echo "PID changed"

  - name: "Exec with no arguments and redirection"
    stdin: |
      exec 3>&1
      echo "Hello, fd 3!" >&3

  - name: "Exec a command"
    stdin: |
      exec $0 -c 'echo "In child shell"'
      echo "This is never reached"

  - name: "exec without -c"
    stdin: |
      export myvar=value
      exec $0 -c 'echo "myvar: ${myvar}"'

  - name: "exec -c"
    stdin: |
      export myvar=value
      exec -c $0 -c 'echo "myvar: ${myvar}"'

  - name: "exec -a"
    stdin: |
      exec -a shellname $0 -c 'echo "0: $0"'
