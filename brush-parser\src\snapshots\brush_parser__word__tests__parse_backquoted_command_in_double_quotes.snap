---
source: brush-parser/src/word.rs
expression: "test_parse(r#\"\"`echo hi`\"\"#)?"
---
ParseTestResults(
  input: "\"`echo hi`\"",
  result: [
    WordPieceWithSource(
      piece: DoubleQuotedSequence([
        WordPieceWithSource(
          piece: BackquotedCommandSubstitution("echo hi"),
          start_index: 1,
          end_index: 10,
        ),
      ]),
      start_index: 0,
      end_index: 11,
    ),
  ],
)
