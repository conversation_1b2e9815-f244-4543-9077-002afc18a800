---
source: brush-parser/src/tokenizer.rs
expression: "test_tokenizer(r\"echo `echo\\`hi`\")?"
---
TokenizerResult(
  input: "echo `echo\\`hi`",
  result: [
    W("echo", Loc(
      start: Pos(
        idx: 0,
        line: 1,
        col: 1,
      ),
      end: Pos(
        idx: 4,
        line: 1,
        col: 5,
      ),
    )),
    W("`echo\\`hi`", Loc(
      start: Pos(
        idx: 5,
        line: 1,
        col: 6,
      ),
      end: Pos(
        idx: 15,
        line: 1,
        col: 16,
      ),
    )),
  ],
)
