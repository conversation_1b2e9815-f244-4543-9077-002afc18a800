---
source: brush-parser/src/word.rs
expression: "test_parse(r#\"$\"a ${b} c\"\"#)?"
---
ParseTestResults(
  input: "$\"a ${b} c\"",
  result: [
    WordPieceWithSource(
      piece: GettextDoubleQuotedSequence([
        WordPieceWithSource(
          piece: Text("a "),
          start_index: 2,
          end_index: 4,
        ),
        WordPieceWithSource(
          piece: ParameterExpansion(Parameter(
            parameter: Named("b"),
            indirect: false,
          )),
          start_index: 4,
          end_index: 8,
        ),
        WordPieceWithSource(
          piece: Text(" c"),
          start_index: 8,
          end_index: 10,
        ),
      ]),
      start_index: 0,
      end_index: 11,
    ),
  ],
)
