[package]
name = "xtask"
publish = false
version = "0.1.0"
authors.workspace = true
categories.workspace = true
edition.workspace = true
keywords.workspace = true
license.workspace = true
readme.workspace = true
repository.workspace = true
rust-version.workspace = true

[lints]
workspace = true

[dependencies]
anyhow = "1.0.98"
brush-shell = { version = "^0.2.21", path = "../brush-shell" }
clap = { version = "4.5.40", features = ["derive"] }
clap_mangen = "0.2.29"
clap-markdown = "0.1.5"
