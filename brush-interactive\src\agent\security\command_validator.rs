//! Command validation and security control for the AI Agent

use crate::agent::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>};
use regex::Regex;
use std::collections::HashSet;

/// Result of command validation
#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct ValidationResult {
    /// Whether the command is allowed to execute
    pub allowed: bool,
    /// Reason for the decision
    pub reason: String,
    /// Whether the command requires user confirmation
    pub requires_confirmation: bool,
}

/// Command validator for security control
pub struct CommandValidator {
    /// Commands that are always allowed (whitelist)
    whitelist: HashSet<String>,
    /// Command patterns that are always allowed
    whitelist_patterns: Vec<Regex>,
    /// Commands that are always blocked (blacklist)
    blacklist: HashSet<String>,
    /// Command patterns that are always blocked
    blacklist_patterns: Vec<Regex>,
    /// Commands that always require confirmation
    always_confirm: HashSet<String>,
    /// Commands that are always blocked
    always_block: HashSet<String>,
    /// Whether to require confirmation for unknown commands
    require_confirmation: bool,
}

impl CommandValidator {
    /// Create a new command validator
    pub fn new(config: &AgentConfig) -> AgentR<PERSON>ult<Self> {
        let mut validator = Self {
            whitelist: HashSet::new(),
            whitelist_patterns: Vec::new(),
            blacklist: HashSet::new(),
            blacklist_patterns: Vec::new(),
            always_confirm: config.security.always_confirm.iter().cloned().collect(),
            always_block: config.security.always_block.iter().cloned().collect(),
            require_confirmation: config.security.require_confirmation,
        };
        
        // Load whitelist if provided
        if let Some(ref whitelist_path) = config.security.whitelist_path {
            validator.load_whitelist(whitelist_path)?;
        }
        
        // Load blacklist if provided
        if let Some(ref blacklist_path) = config.security.blacklist_path {
            validator.load_blacklist(blacklist_path)?;
        }
        
        // Add default safe commands to whitelist
        validator.add_default_whitelist();
        
        Ok(validator)
    }
    
    /// Validate a command for execution
    pub fn validate_command(&self, command: &str) -> ValidationResult {
        let command = command.trim();
        
        // Check if command is always blocked
        if self.is_always_blocked(command) {
            return ValidationResult {
                allowed: false,
                reason: "命令在永久黑名单中".to_string(),
                requires_confirmation: false,
            };
        }
        
        // Check blacklist first
        if self.is_blacklisted(command) {
            return ValidationResult {
                allowed: false,
                reason: "命令在黑名单中".to_string(),
                requires_confirmation: false,
            };
        }
        
        // Check if command always requires confirmation
        if self.is_always_confirm(command) {
            return ValidationResult {
                allowed: true,
                reason: "命令需要用户确认".to_string(),
                requires_confirmation: true,
            };
        }
        
        // Check whitelist
        if self.is_whitelisted(command) {
            return ValidationResult {
                allowed: true,
                reason: "命令在白名单中".to_string(),
                requires_confirmation: false,
            };
        }
        
        // Default behavior based on configuration
        if self.require_confirmation {
            ValidationResult {
                allowed: true,
                reason: "未知命令，需要用户确认".to_string(),
                requires_confirmation: true,
            }
        } else {
            ValidationResult {
                allowed: true,
                reason: "命令被允许执行".to_string(),
                requires_confirmation: false,
            }
        }
    }
    
    /// Check if command is in whitelist
    fn is_whitelisted(&self, command: &str) -> bool {
        // Check exact matches
        if self.whitelist.contains(command) {
            return true;
        }
        
        // Check pattern matches
        for pattern in &self.whitelist_patterns {
            if pattern.is_match(command) {
                return true;
            }
        }
        
        false
    }
    
    /// Check if command is in blacklist
    fn is_blacklisted(&self, command: &str) -> bool {
        // Check exact matches
        if self.blacklist.contains(command) {
            return true;
        }
        
        // Check pattern matches
        for pattern in &self.blacklist_patterns {
            if pattern.is_match(command) {
                return true;
            }
        }
        
        false
    }
    
    /// Check if command is always blocked
    fn is_always_blocked(&self, command: &str) -> bool {
        self.always_block.iter().any(|blocked| command.contains(blocked))
    }
    
    /// Check if command always requires confirmation
    fn is_always_confirm(&self, command: &str) -> bool {
        let command_parts: Vec<&str> = command.split_whitespace().collect();
        if let Some(cmd) = command_parts.first() {
            self.always_confirm.contains(*cmd)
        } else {
            false
        }
    }
    
    /// Load whitelist from file
    fn load_whitelist(&mut self, path: &std::path::Path) -> AgentResult<()> {
        let content = std::fs::read_to_string(path)
            .map_err(|e| AgentError::Config(format!("Failed to read whitelist file: {}", e)))?;
        
        for line in content.lines() {
            let line = line.trim();
            if line.is_empty() || line.starts_with('#') {
                continue;
            }
            
            if line.starts_with("regex:") {
                let pattern = &line[6..];
                match Regex::new(pattern) {
                    Ok(regex) => self.whitelist_patterns.push(regex),
                    Err(e) => tracing::warn!("Invalid whitelist regex pattern '{}': {}", pattern, e),
                }
            } else {
                self.whitelist.insert(line.to_string());
            }
        }
        
        Ok(())
    }
    
    /// Load blacklist from file
    fn load_blacklist(&mut self, path: &std::path::Path) -> AgentResult<()> {
        let content = std::fs::read_to_string(path)
            .map_err(|e| AgentError::Config(format!("Failed to read blacklist file: {}", e)))?;
        
        for line in content.lines() {
            let line = line.trim();
            if line.is_empty() || line.starts_with('#') {
                continue;
            }
            
            if line.starts_with("regex:") {
                let pattern = &line[6..];
                match Regex::new(pattern) {
                    Ok(regex) => self.blacklist_patterns.push(regex),
                    Err(e) => tracing::warn!("Invalid blacklist regex pattern '{}': {}", pattern, e),
                }
            } else {
                self.blacklist.insert(line.to_string());
            }
        }
        
        Ok(())
    }
    
    /// Add default safe commands to whitelist
    fn add_default_whitelist(&mut self) {
        let safe_commands = vec![
            "ls", "dir", "pwd", "cd", "echo", "cat", "head", "tail",
            "grep", "find", "which", "whereis", "type", "file",
            "date", "uptime", "whoami", "id", "groups",
            "ps", "top", "htop", "free", "df", "du",
            "git status", "git log", "git diff", "git branch",
            "npm list", "pip list", "cargo check",
        ];
        
        for cmd in safe_commands {
            self.whitelist.insert(cmd.to_string());
        }
        
        // Add safe patterns
        let safe_patterns = vec![
            r"^ls\s.*",
            r"^git\s+status.*",
            r"^git\s+log.*",
            r"^git\s+diff.*",
            r"^git\s+show.*",
            r"^cat\s+[^|>]*$",
            r"^head\s+[^|>]*$",
            r"^tail\s+[^|>]*$",
        ];
        
        for pattern in safe_patterns {
            if let Ok(regex) = Regex::new(pattern) {
                self.whitelist_patterns.push(regex);
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::agent::config::{AgentConfig, SecurityConfig};
    
    fn create_test_config() -> AgentConfig {
        let mut config = AgentConfig::default();
        config.security = SecurityConfig {
            whitelist_path: None,
            blacklist_path: None,
            require_confirmation: true,
            always_confirm: vec!["rm".to_string(), "mv".to_string()],
            always_block: vec!["rm -rf /".to_string(), "shutdown".to_string()],
        };
        config
    }
    
    #[test]
    fn test_safe_command_validation() {
        let config = create_test_config();
        let validator = CommandValidator::new(&config).unwrap();
        
        let result = validator.validate_command("ls -la");
        assert!(result.allowed);
        assert!(!result.requires_confirmation);
    }
    
    #[test]
    fn test_dangerous_command_validation() {
        let config = create_test_config();
        let validator = CommandValidator::new(&config).unwrap();
        
        let result = validator.validate_command("rm important_file.txt");
        assert!(result.allowed);
        assert!(result.requires_confirmation);
    }
    
    #[test]
    fn test_blocked_command_validation() {
        let config = create_test_config();
        let validator = CommandValidator::new(&config).unwrap();
        
        let result = validator.validate_command("rm -rf /");
        assert!(!result.allowed);
        assert!(!result.requires_confirmation);
    }
    
    #[test]
    fn test_unknown_command_validation() {
        let config = create_test_config();
        let validator = CommandValidator::new(&config).unwrap();
        
        let result = validator.validate_command("unknown_command");
        assert!(result.allowed);
        assert!(result.requires_confirmation);
    }
}
