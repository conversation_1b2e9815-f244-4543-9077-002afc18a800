//! Tool executor for the AI Agent

use crate::agent::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, AgentR<PERSON>ult};
use crate::agent::security::CommandValidator;
use crate::agent::tools::{<PERSON><PERSON>, ToolResult};
use crate::agent::tools::basic_tools::*;
use crate::agent::tools::advanced_tools::*;
use crate::agent::tools::web_tools::*;
use crate::agent::xml_parser::{XmlParser, ToolCall};
use std::collections::HashMap;
use std::sync::Arc;

/// Tool execution result
#[derive(Debug, Clone)]
pub struct ExecutionResult {
    /// Name of the tool that was executed
    pub tool_name: String,
    /// Result of the tool execution
    pub result: String,
    /// Whether the task is completed
    pub task_completed: bool,
    /// Whether user input is required
    pub requires_user_input: bool,
}

/// Tool executor that manages and executes tools
pub struct ToolExecutor {
    /// Registered tools
    tools: HashMap<String, Arc<dyn Tool>>,
    /// XML parser for extracting tool calls
    xml_parser: XmlParser,
    /// Configuration
    config: AgentConfig,
}

impl ToolExecutor {
    /// Create a new tool executor
    pub fn new(config: AgentConfig) -> AgentResult<Self> {
        let mut executor = Self {
            tools: HashMap::new(),
            xml_parser: XmlParser::new()?,
            config: config.clone(),
        };
        
        // Register default tools
        executor.register_default_tools()?;
        
        Ok(executor)
    }
    
    /// Register default tools based on configuration
    fn register_default_tools(&mut self) -> AgentResult<()> {
        let command_validator = CommandValidator::new(&self.config)?;
        
        // Register basic tools
        if self.config.tools.enabled_tools.contains(&"command_exec".to_string()) {
            self.register_tool(Arc::new(CommandExecTool::new(command_validator)));
        }
        
        if self.config.tools.enabled_tools.contains(&"list_dir".to_string()) {
            self.register_tool(Arc::new(ListDirTool));
        }
        
        if self.config.tools.enabled_tools.contains(&"read_file".to_string()) {
            self.register_tool(Arc::new(ReadFileTool));
        }
        
        if self.config.tools.enabled_tools.contains(&"write_file".to_string()) {
            self.register_tool(Arc::new(WriteFileTool));
        }
        
        if self.config.tools.enabled_tools.contains(&"search_files".to_string()) {
            self.register_tool(Arc::new(SearchFilesTool));
        }
        
        // Register advanced tools
        if self.config.tools.enabled_tools.contains(&"ask_question".to_string()) {
            self.register_tool(Arc::new(AskQuestionTool));
        }
        
        if self.config.tools.enabled_tools.contains(&"final_answer".to_string()) {
            self.register_tool(Arc::new(FinalAnswerTool));
        }
        
        if self.config.tools.enabled_tools.contains(&"update_todo_list".to_string()) {
            self.register_tool(Arc::new(UpdateTodoListTool));
        }
        
        if self.config.tools.enabled_tools.contains(&"attempt_completion".to_string()) {
            self.register_tool(Arc::new(AttemptCompletionTool));
        }
        
        self.register_tool(Arc::new(TextProcessTool));
        
        // Register web tools
        if self.config.tools.enabled_tools.contains(&"web_fetch".to_string()) {
            self.register_tool(Arc::new(WebFetchTool::new(&self.config)));
        }
        
        if self.config.tools.enabled_tools.contains(&"http_request".to_string()) {
            self.register_tool(Arc::new(HttpRequestTool::new()));
        }
        
        Ok(())
    }
    
    /// Register a tool
    pub fn register_tool(&mut self, tool: Arc<dyn Tool>) {
        self.tools.insert(tool.name().to_string(), tool);
    }
    
    /// Execute a tool call from XML
    pub fn execute_tool(&self, tool_call_xml: &str) -> AgentResult<ExecutionResult> {
        // Parse the XML to extract tool call
        let tool_call = self.xml_parser.extract_tool_call(tool_call_xml)?;
        
        // Execute the tool
        self.execute_tool_call(&tool_call)
    }
    
    /// Execute a parsed tool call
    pub fn execute_tool_call(&self, tool_call: &ToolCall) -> AgentResult<ExecutionResult> {
        let tool = self.tools.get(&tool_call.tool_name)
            .ok_or_else(|| AgentError::ToolExecution(format!("Unknown tool: {}", tool_call.tool_name)))?;
        
        let result = tool.execute(tool_call.parameters.clone())?;
        
        // Determine if task is completed or requires user input
        let (task_completed, requires_user_input) = self.analyze_tool_result(&tool_call.tool_name, &result);
        
        Ok(ExecutionResult {
            tool_name: tool_call.tool_name.clone(),
            result: result.to_json(),
            task_completed,
            requires_user_input,
        })
    }
    
    /// Analyze tool result to determine next steps
    fn analyze_tool_result(&self, tool_name: &str, result: &ToolResult) -> (bool, bool) {
        // Check if result data indicates completion or user input requirement
        if let Some(data) = &result.data {
            if let Some(task_completed) = data.get("task_completed") {
                if task_completed.as_bool().unwrap_or(false) {
                    return (true, false);
                }
            }
            
            if let Some(requires_user_input) = data.get("requires_user_input") {
                if requires_user_input.as_bool().unwrap_or(false) {
                    return (false, true);
                }
            }
        }
        
        // Check based on tool name and result status
        match tool_name {
            "ask_question" => (false, true),
            "final_answer" | "attempt_completion" => (true, false),
            _ => {
                if result.requires_confirmation() {
                    (false, true)
                } else {
                    (false, false)
                }
            }
        }
    }
    
    /// Check if text contains tool calls
    pub fn contains_tool_call(&self, text: &str) -> bool {
        self.xml_parser.contains_tool_call(text)
    }
    
    /// Get list of available tools
    pub fn get_available_tools(&self) -> Vec<String> {
        self.tools.keys().cloned().collect()
    }
    
    /// Get tool description
    pub fn get_tool_description(&self, tool_name: &str) -> Option<String> {
        self.tools.get(tool_name).map(|tool| tool.description().to_string())
    }
    
    /// Execute multiple tool calls (returns first one for now)
    pub fn execute_multiple_tools(&self, tool_call_xml: &str) -> AgentResult<Vec<ExecutionResult>> {
        let tool_calls = self.xml_parser.extract_all_tool_calls(tool_call_xml)?;
        
        if tool_calls.is_empty() {
            return Err(AgentError::ToolExecution("No tool calls found".to_string()));
        }
        
        // For now, only execute the first tool call
        let result = self.execute_tool_call(&tool_calls[0])?;
        
        if tool_calls.len() > 1 {
            // Modify result to indicate only first tool was executed
            let mut modified_result = result;
            modified_result.result = format!(
                "注意：检测到多个工具调用，只执行了第一个工具 '{}'。\n\n{}",
                tool_calls[0].tool_name,
                modified_result.result
            );
            Ok(vec![modified_result])
        } else {
            Ok(vec![result])
        }
    }
}
