name: "Pipeline"
cases:
  - name: "Basic pipe"
    stdin: |
      echo hi | grep -l h

  - name: "Longer pipe"
    stdin: |
      echo hi | grep h | wc -l

  - name: "Invert result"
    stdin: |
      ! false
      echo "! false: $?"
      ! true
      echo "! true: $?"

  - name: "Exit codes for piped commands"
    test_files:
      - path: "script.sh"
        executable: true
        contents: |
          #!/bin/sh
          (cat; echo -n "-> $1")
          exit $1
    stdin: |
      ./script.sh 10 | ./script.sh 0 | ./script.sh 33

  - name: "Side effects in pipe commands"
    stdin: |
      var=0
      echo "var: ${var}"
      { var=1; }
      echo "var: ${var}"
      { var=2; echo hi; } | cat
      echo "var: ${var}"
      echo hi | { var=3; cat; }
      echo "var: ${var}"

  - name: "pipeline extension"
    stdin: |
      echo -e "hello" |& wc -l
      cat dfdfgdfgdf |& wc -l
      foo() { cat dfgdfg; } |& wc -l
