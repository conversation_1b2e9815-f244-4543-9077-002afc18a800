#!/usr/bin/env python3
"""
AI Agent 演示脚本
展示如何使用集成到Brush Shell中的AI Agent
"""

import os
import json

def create_demo_config():
    """创建演示配置文件"""
    print("📝 创建AI Agent演示配置...")
    
    # 创建配置目录
    config_dir = os.path.expanduser("~/.config/brush")
    os.makedirs(config_dir, exist_ok=True)
    
    # 演示配置
    demo_config = {
        "openai": {
            "base_url": "https://api.openai.com/v1",
            "model_id": "gpt-4",
            "api_key": "${OPENAI_API_KEY}",
            "max_retries": 3,
            "timeout_seconds": 30
        },
        "prompt": {
            "template": "你是一个智能的命令行助手，能够帮助用户执行各种系统操作。\\n\\n当前环境信息：\\n{ENVIRONMENT_DETAIL}\\n\\n你可以使用以下工具：\\n{TOOL_DESCRIPTIONS}\\n\\n请根据用户的输入选择合适的工具来完成任务。每次只能调用一个工具。\\n如果没有合适的工具，请使用ask_question工具询问更多信息，或使用final_answer工具给出最终回答。\\n\\n重要要求：\\n1. 必须使用工具调用，不能直接回答\\n2. 每次只能调用一个工具\\n3. 工具调用必须使用XML格式\\n4. 对于危险操作，要谨慎处理并询问确认",
            "tool_descriptions": {
                "ask_question": {
                    "description": "当需要特定信息才能继续时使用（用户未提供足够细节时）",
                    "example": "<ask_question>\\n<question>您遇到问题的设备序列号是多少？</question>\\n</ask_question>",
                    "requirements": "尽早提问，获取必要信息"
                },
                "final_answer": {
                    "description": "当确认掌握足够信息时给出最终答复",
                    "example": "<final_answer>\\n<answer>错误代码EC205表示电池过热，建议暂停使用并检查散热孔</answer>\\n<references>\\n<reference>/docs/error_codes/EC205</reference>\\n</references>\\n</final_answer>",
                    "requirements": "提供结构化解决方案和相关参考"
                }
            }
        },
        "tools": {
            "enabled_tools": [
                "command_exec",
                "list_dir", 
                "read_file",
                "write_file",
                "search_files",
                "ask_question",
                "final_answer",
                "web_fetch",
                "update_todo_list"
            ]
        },
        "security": {
            "require_confirmation": True,
            "always_confirm": [
                "rm",
                "rmdir", 
                "mv",
                "cp",
                "chmod",
                "chown"
            ],
            "always_block": [
                "rm -rf /",
                "shutdown",
                "reboot",
                "halt"
            ]
        },
        "web_fetch": {
            "timeout_seconds": 30,
            "max_content_length": 50000
        }
    }
    
    # 保存配置文件
    config_path = os.path.join(config_dir, "agent_config.json")
    with open(config_path, "w", encoding="utf-8") as f:
        json.dump(demo_config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 配置文件已创建: {config_path}")
    return config_path

def show_usage_examples():
    """显示使用示例"""
    print("\n🎯 AI Agent 使用示例:")
    print("="*50)
    
    examples = [
        {
            "description": "文件操作",
            "commands": [
                "帮我创建一个名为test.txt的文件，内容是'Hello World'",
                "列出当前目录的所有文件",
                "读取README.md文件的内容"
            ]
        },
        {
            "description": "搜索和分析",
            "commands": [
                "在当前目录下搜索包含'TODO'的文件",
                "分析这个项目的结构",
                "找出所有的Rust源文件"
            ]
        },
        {
            "description": "系统操作",
            "commands": [
                "检查系统磁盘使用情况",
                "显示当前运行的进程",
                "查看系统信息"
            ]
        },
        {
            "description": "网络操作",
            "commands": [
                "获取https://httpbin.org/json的内容",
                "检查网络连接状态",
                "下载一个小文件"
            ]
        }
    ]
    
    for example in examples:
        print(f"\n📂 {example['description']}:")
        for cmd in example['commands']:
            print(f"  brush> {cmd}")
    
    print("\n💡 提示:")
    print("- 当输入无法识别的命令时，AI Agent会自动接管处理")
    print("- AI Agent会根据需要调用相应的工具来完成任务")
    print("- 危险操作会要求用户确认")
    print("- 所有操作都有安全控制和日志记录")

def show_setup_instructions():
    """显示设置说明"""
    print("\n🔧 设置说明:")
    print("="*50)
    
    print("\n1. 设置API密钥:")
    print("   export OPENAI_API_KEY='your-openai-api-key-here'")
    print("   # 或者在Windows中:")
    print("   set OPENAI_API_KEY=your-openai-api-key-here")
    
    print("\n2. 启动Brush Shell:")
    print("   ./target/release/brush")
    print("   # 或者在Windows中:")
    print("   .\\target\\release\\brush.exe")
    
    print("\n3. 测试AI Agent:")
    print("   brush> help me create a file")
    print("   brush> 列出当前目录的文件")
    print("   brush> 分析这个项目")
    
    print("\n4. 查看日志:")
    print("   RUST_LOG=info ./target/release/brush")

def show_architecture_info():
    """显示架构信息"""
    print("\n🏗️ AI Agent 架构:")
    print("="*50)
    
    print("""
📦 核心组件:
├── Agent Core (agent/core.rs)
│   ├── 处理用户输入
│   ├── 管理对话循环  
│   └── 协调各个组件
│
├── Conversation Manager (agent/conversation_manager.rs)
│   ├── 维护对话历史
│   ├── 收集环境信息
│   └── 构建系统提示词
│
├── Tool Executor (agent/tool_executor.rs)
│   ├── 注册和管理工具
│   ├── 解析工具调用
│   └── 执行工具并返回结果
│
├── XML Parser (agent/xml_parser.rs)
│   ├── 提取工具调用
│   ├── 解析参数
│   └── 验证XML结构
│
├── Security Manager (agent/security/)
│   ├── 命令验证
│   ├── 黑白名单检查
│   └── 用户确认机制
│
└── Tools (agent/tools/)
    ├── 基础工具 (文件操作、命令执行)
    ├── 高级工具 (问答、任务管理)
    └── 网络工具 (网页抓取、HTTP请求)

🔄 工作流程:
1. 用户输入无效命令
2. Shell触发AI Agent
3. Agent调用AI模型
4. 模型返回工具调用
5. 执行相应工具
6. 返回结果给用户
""")

def main():
    """主函数"""
    print("🤖 Brush Shell AI Agent 演示")
    print("="*60)
    
    # 创建演示配置
    config_path = create_demo_config()
    
    # 显示架构信息
    show_architecture_info()
    
    # 显示使用示例
    show_usage_examples()
    
    # 显示设置说明
    show_setup_instructions()
    
    print("\n" + "="*60)
    print("🎉 AI Agent 已成功集成到Brush Shell!")
    print("📋 功能特性:")
    print("  ✅ 智能命令处理")
    print("  ✅ 多种工具支持")
    print("  ✅ 安全控制机制")
    print("  ✅ 配置管理系统")
    print("  ✅ 完整的测试覆盖")
    print("\n💡 现在您可以享受智能命令行体验了！")

if __name__ == "__main__":
    main()
