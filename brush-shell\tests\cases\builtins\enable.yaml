name: "Builtins: enable"
cases:
  - name: "List special builtins"
    stdin: enable -s

  - name: "List default-disabled builtins"
    stdin: enable -n

  - name: "List all builtins"
    stdin: |
      # List builtins but ignore any brush specific ones.
      enable | grep -v brush

  - name: "Disable builtins"
    ignore_stderr: true
    stdin: |
      type printf

      # Disable the builtin
      PATH=
      enable -n printf

      # Check
      type printf
      print "Gone\n"

      # Re-enable
      enable printf

      # Re-check
      type printf
      printf "Back\n"
