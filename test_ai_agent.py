#!/usr/bin/env python3
"""
测试AI Agent功能的脚本
"""

import subprocess
import json
import os
import time
import sys

def run_command(cmd, timeout=10):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            capture_output=True, 
            text=True, 
            timeout=timeout
        )
        return result.returncode, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "Command timed out"

def test_compilation():
    """测试项目编译"""
    print("🔨 测试项目编译...")
    
    # 编译项目
    returncode, stdout, stderr = run_command("cargo build", timeout=120)
    
    if returncode == 0:
        print("✅ 项目编译成功")
        return True
    else:
        print("❌ 项目编译失败")
        print(f"错误输出: {stderr}")
        return False

def test_unit_tests():
    """运行单元测试"""
    print("\n🧪 运行单元测试...")
    
    returncode, stdout, stderr = run_command("cargo test", timeout=60)
    
    if returncode == 0:
        print("✅ 单元测试通过")
        print(f"测试输出: {stdout}")
        return True
    else:
        print("❌ 单元测试失败")
        print(f"错误输出: {stderr}")
        return False

def test_config_creation():
    """测试配置文件创建"""
    print("\n📝 测试配置文件...")
    
    config_path = "test_agent_config.json"
    
    # 创建测试配置
    test_config = {
        "openai": {
            "base_url": "https://api.openai.com/v1",
            "model_id": "gpt-4",
            "api_key": "test-key-12345",
            "max_retries": 3,
            "timeout_seconds": 30
        },
        "tools": {
            "enabled_tools": [
                "ask_question",
                "final_answer",
                "list_dir",
                "read_file"
            ]
        },
        "security": {
            "require_confirmation": True,
            "always_confirm": ["rm", "mv"],
            "always_block": ["rm -rf /"]
        }
    }
    
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(test_config, f, indent=2, ensure_ascii=False)
        
        # 验证配置文件可以读取
        with open(config_path, 'r', encoding='utf-8') as f:
            loaded_config = json.load(f)
        
        if loaded_config["openai"]["model_id"] == "gpt-4":
            print("✅ 配置文件创建和读取成功")
            os.remove(config_path)  # 清理测试文件
            return True
        else:
            print("❌ 配置文件内容验证失败")
            return False
            
    except Exception as e:
        print(f"❌ 配置文件测试失败: {e}")
        return False

def test_xml_parsing():
    """测试XML解析功能"""
    print("\n🔍 测试XML解析...")
    
    # 创建一个简单的Rust测试程序
    test_code = '''
use brush_interactive::agent::xml_parser::XmlParser;

fn main() {
    let parser = XmlParser::new().unwrap();
    let xml = r#"<ask_question>
<question>What is your name?</question>
</ask_question>"#;
    
    match parser.extract_tool_call(xml) {
        Ok(tool_call) => {
            println!("Tool: {}", tool_call.tool_name);
            println!("Question: {}", tool_call.parameters.get("question").unwrap_or(&"None".to_string()));
        }
        Err(e) => {
            eprintln!("Error: {}", e);
            std::process::exit(1);
        }
    }
}
'''
    
    # 写入测试文件
    test_file = "test_xml_parser.rs"
    try:
        with open(test_file, 'w') as f:
            f.write(test_code)
        
        # 编译并运行测试
        compile_cmd = f"rustc --extern brush_interactive=target/debug/deps/libbrush_interactive-*.rlib {test_file} -o test_xml_parser"
        returncode, stdout, stderr = run_command(compile_cmd)
        
        if returncode == 0:
            returncode, stdout, stderr = run_command("./test_xml_parser")
            if returncode == 0 and "ask_question" in stdout:
                print("✅ XML解析测试通过")
                os.remove(test_file)
                if os.path.exists("test_xml_parser"):
                    os.remove("test_xml_parser")
                return True
        
        print("❌ XML解析测试失败")
        print(f"编译输出: {stderr}")
        return False
        
    except Exception as e:
        print(f"❌ XML解析测试异常: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n⚙️ 测试基本功能...")
    
    # 检查是否可以创建brush可执行文件
    if os.path.exists("target/debug/brush"):
        print("✅ brush可执行文件存在")
        
        # 尝试运行help命令
        returncode, stdout, stderr = run_command("./target/debug/brush --help", timeout=5)
        if returncode == 0:
            print("✅ brush --help 命令正常")
            return True
        else:
            print("❌ brush --help 命令失败")
            return False
    else:
        print("❌ brush可执行文件不存在")
        return False

def main():
    """主测试函数"""
    print("🚀 开始AI Agent功能测试\n")
    
    tests = [
        ("编译测试", test_compilation),
        ("单元测试", test_unit_tests),
        ("配置文件测试", test_config_creation),
        ("基本功能测试", test_basic_functionality),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！AI Agent已准备就绪。")
        print("\n📋 下一步:")
        print("1. 设置OPENAI_API_KEY环境变量")
        print("2. 运行 ./target/debug/brush 启动shell")
        print("3. 输入无效命令测试AI Agent功能")
        return 0
    else:
        print("⚠️ 部分测试失败，请检查错误信息。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
