---
source: brush-parser/src/tokenizer.rs
expression: "test_tokenizer(\"1 2 3\")?"
---
TokenizerR<PERSON>ult(
  input: "1 2 3",
  result: [
    W("1", Loc(
      start: Pos(
        idx: 0,
        line: 1,
        col: 1,
      ),
      end: Pos(
        idx: 1,
        line: 1,
        col: 2,
      ),
    )),
    W("2", Loc(
      start: Pos(
        idx: 2,
        line: 1,
        col: 3,
      ),
      end: Pos(
        idx: 3,
        line: 1,
        col: 4,
      ),
    )),
    W("3", Loc(
      start: Pos(
        idx: 4,
        line: 1,
        col: 5,
      ),
      end: Pos(
        idx: 5,
        line: 1,
        col: 6,
      ),
    )),
  ],
)
